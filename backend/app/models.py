# -*- coding: utf-8 -*-
from datetime import datetime
import json
import uuid
import hashlib
from . import db

# =============================================================================
# 基础表 - 不依赖其他表
# =============================================================================

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), nullable=False, default='未命名')
    phone = db.Column(db.String(20), unique=True, nullable=True)
    password_hash = db.Column(db.String(128), nullable=True)
    email = db.Column(db.String(120), unique=True, nullable=True)
    wechat_openid = db.Column(db.String(64), unique=True, nullable=True)
    alipay_userid = db.Column(db.String(64), unique=True, nullable=True)
    avatar_url = db.Column(db.String(255), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    companies = db.relationship('Company', back_populates='user', cascade='all, delete-orphan')
    bid_histories = db.relationship('BidHistory', back_populates='user', cascade='all, delete-orphan')

    def set_password(self, password):
        """设置用户密码"""
        from werkzeug.security import generate_password_hash
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """检查密码是否正确"""
        from werkzeug.security import check_password_hash
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'phone': self.phone,
            'email': self.email,
            'wechat_openid': self.wechat_openid,
            'alipay_userid': self.alipay_userid,
            'avatar_url': self.avatar_url,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class FileStorage(db.Model):
    """文件存储表 - 根据hash值去重存储文件"""
    __tablename__ = 'file_storage'

    id = db.Column(db.Integer, primary_key=True)
    file_hash = db.Column(db.String(64), unique=True, nullable=False)  # SHA256 hash值
    original_filename = db.Column(db.String(255), nullable=False)  # 原始文件名
    file_size = db.Column(db.Integer, nullable=False)  # 文件大小（字节）
    file_type = db.Column(db.String(50), nullable=False)  # 文件类型
    mime_type = db.Column(db.String(100), nullable=True)  # MIME类型
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'file_hash': self.file_hash,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'mime_type': self.mime_type,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    @property
    def file_path(self):
        """根据hash值和原始文件名生成文件路径"""
        return f"uploads/{self.file_hash}/{self.original_filename}"

    @staticmethod
    def calculate_file_hash(file_content):
        """计算文件内容的SHA256 hash值"""
        return hashlib.sha256(file_content).hexdigest()

# =============================================================================
# 用户相关表 - 依赖User表
# =============================================================================

class Company(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    unified_social_credit_code = db.Column(db.String(50), unique=True, nullable=True)  # 统一社会信用代码
    legal_representative = db.Column(db.String(100), nullable=True)  # 法定代表人
    registered_capital = db.Column(db.String(100), nullable=True)  # 注册资本
    business_scope = db.Column(db.Text, nullable=True)  # 经营范围
    address = db.Column(db.String(500), nullable=True)  # 注册地址
    contact_phone = db.Column(db.String(50), nullable=True)  # 联系电话
    contact_email = db.Column(db.String(120), nullable=True)  # 联系邮箱
    website = db.Column(db.String(255), nullable=True)  # 官网
    logo_url = db.Column(db.String(255), nullable=True)  # 公司logo
    description = db.Column(db.Text, nullable=True)  # 公司简介
    is_verified = db.Column(db.Boolean, default=False)  # 是否认证
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 外键
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 公司属于一个用户

    # 关系
    user = db.relationship('User', back_populates='companies')
    bids = db.relationship('Bid', back_populates='company', cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'unified_social_credit_code': self.unified_social_credit_code,
            'legal_representative': self.legal_representative,
            'registered_capital': self.registered_capital,
            'business_scope': self.business_scope,
            'address': self.address,
            'contact_phone': self.contact_phone,
            'contact_email': self.contact_email,
            'website': self.website,
            'logo_url': self.logo_url,
            'description': self.description,
            'is_verified': self.is_verified,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# =============================================================================
# 信息类型表 - 不依赖其他表
# =============================================================================

class CompanyInformationType(db.Model):
    """公司信息类型表"""
    __tablename__ = 'company_information_type'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)  # 信息名称，如"公司简介"、"资质证书"等
    description = db.Column(db.Text, nullable=True)  # 信息说明
    category = db.Column(db.String(50), nullable=True)  # 信息分类，如"基本信息"、"资质信息"等
    data_type = db.Column(db.String(20), default='text')  # 数据类型：text, number, date, json等
    is_required = db.Column(db.Boolean, default=False)  # 是否必填
    sort_order = db.Column(db.Integer, default=0)  # 排序顺序
    remarks = db.Column(db.Text, nullable=True)  # 备注
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    company_informations = db.relationship('CompanyInformation', back_populates='information_type', cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'data_type': self.data_type,
            'is_required': self.is_required,
            'sort_order': self.sort_order,
            'remarks': self.remarks,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class TenderInformationType(db.Model):
    """招标文件信息类型表"""
    __tablename__ = 'tender_information_type'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)  # 信息名称，如"项目名称"、"投标截止时间"等
    description = db.Column(db.Text, nullable=True)  # 信息说明
    category = db.Column(db.String(50), nullable=True)  # 信息分类，如"项目信息"、"时间要求"等
    data_type = db.Column(db.String(20), default='text')  # 数据类型：text, number, date, json等
    is_required = db.Column(db.Boolean, default=False)  # 是否必填
    sort_order = db.Column(db.Integer, default=0)  # 排序顺序
    remarks = db.Column(db.Text, nullable=True)  # 备注
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    tender_informations = db.relationship('TenderInformation', back_populates='information_type', cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'data_type': self.data_type,
            'is_required': self.is_required,
            'sort_order': self.sort_order,
            'remarks': self.remarks,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# =============================================================================
# 信息表 - 依赖Company和信息类型表
# =============================================================================

class CompanyInformation(db.Model):
    """公司信息表 - 存储公司的详细信息"""
    __tablename__ = 'company_information'

    id = db.Column(db.Integer, primary_key=True)
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)
    information_type_id = db.Column(db.Integer, db.ForeignKey('company_information_type.id'), nullable=False)
    content = db.Column(db.Text, nullable=True)  # 信息内容
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    company = db.relationship('Company', backref='informations')
    information_type = db.relationship('CompanyInformationType', back_populates='company_informations')

    # 唯一约束：一个公司的同一种信息类型只能有一条记录
    __table_args__ = (db.UniqueConstraint('company_id', 'information_type_id'),)

    def to_dict(self):
        return {
            'id': self.id,
            'company_id': self.company_id,
            'information_type_id': self.information_type_id,
            'information_type_name': self.information_type.name if self.information_type else None,
            'content': self.content,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# =============================================================================
# 招标文件相关表 - 依赖FileStorage表
# =============================================================================

class TenderDocument(db.Model):
    """招标文件 - 全局的，不关联用户或公司"""
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)  # 招标文件标题

    # 外键
    file_id = db.Column(db.Integer, db.ForeignKey('file_storage.id'), nullable=False)  # 关联到文件存储表

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    file = db.relationship('FileStorage', backref='tender_documents')
    informations = db.relationship('TenderInformation', back_populates='tender_document', cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'file_id': self.file_id,
            'filename': self.file.original_filename if self.file else None,
            'file_size': self.file.file_size if self.file else None,
            'file_type': self.file.file_type if self.file else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class TenderInformation(db.Model):
    """招标文件信息表 - 存储招标文件解析后的详细信息"""
    __tablename__ = 'tender_information'

    id = db.Column(db.Integer, primary_key=True)
    tender_document_id = db.Column(db.Integer, db.ForeignKey('tender_document.id'), nullable=False)
    information_type_id = db.Column(db.Integer, db.ForeignKey('tender_information_type.id'), nullable=False)
    content = db.Column(db.Text, nullable=True)  # 信息内容
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    tender_document = db.relationship('TenderDocument', back_populates='informations')
    information_type = db.relationship('TenderInformationType', back_populates='tender_informations')

    # 唯一约束：一个招标文件的同一种信息类型只能有一条记录
    __table_args__ = (db.UniqueConstraint('tender_document_id', 'information_type_id'),)

    def to_dict(self):
        return {
            'id': self.id,
            'tender_document_id': self.tender_document_id,
            'information_type_id': self.information_type_id,
            'information_type_name': self.information_type.name if self.information_type else None,
            'content': self.content,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# =============================================================================
# 标书相关表 - 依赖Company和TenderDocument表
# =============================================================================

class Bid(db.Model):
    """标书"""
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    content = db.Column(db.Text, nullable=True)  # 标书内容
    status = db.Column(db.String(50), default='draft')  # draft, in_progress, completed, submitted
    tender_deadline = db.Column(db.DateTime, nullable=True)  # 投标截止时间
    project_budget = db.Column(db.String(100), nullable=True)  # 项目预算
    project_location = db.Column(db.String(200), nullable=True)  # 项目地点
    contact_person = db.Column(db.String(100), nullable=True)  # 联系人
    contact_phone = db.Column(db.String(50), nullable=True)  # 联系电话

    # 外键
    company_id = db.Column(db.Integer, db.ForeignKey('company.id'), nullable=False)  # 标书必须属于一个公司
    source_file_id = db.Column(db.Integer, db.ForeignKey('file_storage.id'), nullable=True)  # 上传的标书文件，为空表示自己创建的标书
    tender_id = db.Column(db.Integer, db.ForeignKey('tender_document.id'), nullable=True)  # 关联的招标文件，可为空
    root_node_id = db.Column(db.String(36), db.ForeignKey('bid_node.id'), nullable=True)  # 根节点ID

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    company = db.relationship('Company', back_populates='bids')
    source_file = db.relationship('FileStorage', backref='bids')
    tender_document = db.relationship('TenderDocument', backref='bids')
    root_node = db.relationship('BidNode', foreign_keys=[root_node_id], post_update=True)
    nodes = db.relationship('BidNode', foreign_keys='BidNode.bid_id', back_populates='bid', cascade='all, delete-orphan')
    histories = db.relationship('BidHistory', back_populates='bid', cascade='all, delete-orphan')

    # 标书引用关系
    referenced_by = db.relationship('BidReference', foreign_keys='BidReference.source_bid_id', back_populates='source_bid', cascade='all, delete-orphan')
    references = db.relationship('BidReference', foreign_keys='BidReference.target_bid_id', back_populates='target_bid', cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'content': self.content,
            'status': self.status,
            'tender_deadline': self.tender_deadline.isoformat() if self.tender_deadline else None,
            'project_budget': self.project_budget,
            'project_location': self.project_location,
            'contact_person': self.contact_person,
            'contact_phone': self.contact_phone,
            'root_node_id': self.root_node_id,
            'company_id': self.company_id,
            'source_file_id': self.source_file_id,
            'tender_id': self.tender_id,
            'is_uploaded': self.source_file_id is not None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    def get_root_node(self):
        """获取根节点"""
        if self.root_node_id:
            return BidNode.query.get(self.root_node_id)
        return None

    def create_root_node(self):
        """创建根节点"""
        if not self.root_node_id:
            root_node = BidNode(
                node_type='container',
                content=json.dumps({'title': self.title}),
                bid_id=self.id,
                order_index=0
            )
            db.session.add(root_node)
            db.session.flush()  # 获取ID
            self.root_node_id = root_node.id
            return root_node
        return self.get_root_node()

class BidReference(db.Model):
    """标书引用关系表 - 表示某个标书对其他标书的引用关系"""
    __tablename__ = 'bid_reference'

    id = db.Column(db.Integer, primary_key=True)
    source_bid_id = db.Column(db.Integer, db.ForeignKey('bid.id'), nullable=False)  # 引用方标书ID
    target_bid_id = db.Column(db.Integer, db.ForeignKey('bid.id'), nullable=False)  # 被引用方标书ID
    reference_type = db.Column(db.String(50), default='reference')  # 引用类型：reference(参考), template(模板), copy(复制)等
    description = db.Column(db.Text, nullable=True)  # 引用说明
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 关系
    source_bid = db.relationship('Bid', foreign_keys=[source_bid_id], back_populates='referenced_by')
    target_bid = db.relationship('Bid', foreign_keys=[target_bid_id], back_populates='references')

    # 唯一约束：同一对标书的同一种引用关系只能有一条记录
    __table_args__ = (
        db.UniqueConstraint('source_bid_id', 'target_bid_id', 'reference_type'),
        db.CheckConstraint('source_bid_id != target_bid_id', name='check_no_self_reference')  # 不能引用自己
    )

    def to_dict(self):
        return {
            'id': self.id,
            'source_bid_id': self.source_bid_id,
            'target_bid_id': self.target_bid_id,
            'reference_type': self.reference_type,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

# =============================================================================
# 标书节点相关表 - 依赖Bid表
# =============================================================================

class BidNode(db.Model):
    """标书内容节点"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    node_type = db.Column(db.String(50), nullable=False)  # text, image, table, container
    content = db.Column(db.Text, nullable=True)  # JSON格式存储内容
    node_metadata = db.Column(db.Text, nullable=True)  # JSON格式存储元数据（样式、属性等）
    order_index = db.Column(db.Integer, default=0)  # 在父节点中的顺序

    # 外键
    bid_id = db.Column(db.Integer, db.ForeignKey('bid.id'), nullable=False)
    parent_intermediate_id = db.Column(db.String(36), db.ForeignKey('bid_intermediate.id'), nullable=True)

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 关系
    bid = db.relationship('Bid', foreign_keys=[bid_id], back_populates='nodes')
    parent_intermediate = db.relationship('BidIntermediate', back_populates='child_nodes', foreign_keys=[parent_intermediate_id])
    child_intermediates = db.relationship('BidIntermediate', back_populates='parent_node', foreign_keys='BidIntermediate.parent_node_id', cascade='all, delete-orphan')

    def to_dict(self, include_children=False):
        result = {
            'id': self.id,
            'node_type': self.node_type,
            'content': json.loads(self.content) if self.content else None,
            'metadata': json.loads(self.node_metadata) if self.node_metadata else None,
            'order_index': self.order_index,
            'bid_id': self.bid_id,
            'parent_intermediate_id': self.parent_intermediate_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

        if include_children:
            result['children'] = self.get_children_dict()

        return result

    def get_children(self):
        """获取子节点列表，按order_index排序"""
        children = []
        for intermediate in self.child_intermediates:
            if intermediate.child_node:
                children.append(intermediate.child_node)
        return sorted(children, key=lambda x: x.order_index)

    def get_children_dict(self):
        """获取子节点的字典表示"""
        return [child.to_dict(include_children=True) for child in self.get_children()]

    def add_child(self, child_node, user_id):
        """添加子节点"""
        # 创建中间表记录
        intermediate = BidIntermediate(
            parent_node_id=self.id,
            child_node_id=child_node.id
        )
        db.session.add(intermediate)
        db.session.flush()

        # 记录历史
        history = BidHistory(
            action_type='create',
            description=f'添加子节点: {child_node.node_type}',
            new_child_node_id=child_node.id,
            intermediate_id=intermediate.id,
            bid_id=self.bid_id,
            user_id=user_id
        )
        db.session.add(history)

        return intermediate

    def update_content(self, new_content, new_metadata=None, user_id=None):
        """更新节点内容（增量式）"""
        # 创建新节点
        new_node = BidNode(
            node_type=self.node_type,
            content=json.dumps(new_content) if new_content else None,
            node_metadata=json.dumps(new_metadata) if new_metadata else self.node_metadata,
            order_index=self.order_index,
            bid_id=self.bid_id,
            parent_intermediate_id=self.parent_intermediate_id
        )
        db.session.add(new_node)
        db.session.flush()

        # 更新中间表指向新节点
        if self.parent_intermediate:
            old_child_id = self.parent_intermediate.child_node_id
            self.parent_intermediate.child_node_id = new_node.id

            # 记录历史
            if user_id:
                history = BidHistory(
                    action_type='update',
                    description=f'更新节点内容: {self.node_type}',
                    old_child_node_id=old_child_id,
                    new_child_node_id=new_node.id,
                    intermediate_id=self.parent_intermediate.id,
                    bid_id=self.bid_id,
                    user_id=user_id
                )
                db.session.add(history)

        return new_node

class BidIntermediate(db.Model):
    """标书节点中间表"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))

    # 外键
    parent_node_id = db.Column(db.String(36), db.ForeignKey('bid_node.id'), nullable=False)
    child_node_id = db.Column(db.String(36), db.ForeignKey('bid_node.id'), nullable=False)

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 关系
    parent_node = db.relationship('BidNode', back_populates='child_intermediates', foreign_keys=[parent_node_id])
    child_node = db.relationship('BidNode', foreign_keys=[child_node_id])
    child_nodes = db.relationship('BidNode', back_populates='parent_intermediate', foreign_keys='BidNode.parent_intermediate_id')
    histories = db.relationship('BidHistory', back_populates='intermediate', cascade='all, delete-orphan')

    def to_dict(self):
        return {
            'id': self.id,
            'parent_node_id': self.parent_node_id,
            'child_node_id': self.child_node_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class BidHistory(db.Model):
    """标书编辑历史记录"""
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    action_type = db.Column(db.String(50), nullable=False)  # create, update, delete, move
    description = db.Column(db.String(500), nullable=True)  # 操作描述

    # 记录变更前的状态
    old_child_node_id = db.Column(db.String(36), nullable=True)
    new_child_node_id = db.Column(db.String(36), nullable=True)

    # 外键
    intermediate_id = db.Column(db.String(36), db.ForeignKey('bid_intermediate.id'), nullable=False)
    bid_id = db.Column(db.Integer, db.ForeignKey('bid.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 关系
    intermediate = db.relationship('BidIntermediate', back_populates='histories')
    bid = db.relationship('Bid', back_populates='histories')
    user = db.relationship('User', back_populates='bid_histories')

    def to_dict(self):
        return {
            'id': self.id,
            'action_type': self.action_type,
            'description': self.description,
            'old_child_node_id': self.old_child_node_id,
            'new_child_node_id': self.new_child_node_id,
            'intermediate_id': self.intermediate_id,
            'bid_id': self.bid_id,
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def undo(self):
        """撤销操作"""
        if self.action_type in ['create', 'update'] and self.old_child_node_id:
            # 恢复到旧节点
            self.intermediate.child_node_id = self.old_child_node_id
            return True
        elif self.action_type == 'delete' and self.old_child_node_id:
            # 恢复被删除的节点
            self.intermediate.child_node_id = self.old_child_node_id
            return True
        return False

    def redo(self):
        """重做操作"""
        if self.action_type in ['create', 'update'] and self.new_child_node_id:
            # 应用新节点
            self.intermediate.child_node_id = self.new_child_node_id
            return True
        elif self.action_type == 'delete':
            # 重新删除节点
            self.intermediate.child_node_id = None
            return True
        return False

    @staticmethod
    def get_bid_history(bid_id, limit=50):
        """获取标书的编辑历史"""
        return BidHistory.query.filter_by(bid_id=bid_id)\
            .order_by(BidHistory.created_at.desc())\
            .limit(limit).all()

    @staticmethod
    def undo_last_action(bid_id, user_id):
        """撤销最后一个操作"""
        last_history = BidHistory.query.filter_by(bid_id=bid_id, user_id=user_id)\
            .order_by(BidHistory.created_at.desc()).first()

        if last_history and last_history.undo():
            db.session.commit()
            return last_history
        return None


