from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models import Bid, BidNode, BidIntermediate, BidHistory, Company
import json
import uuid

node_bp = Blueprint('nodes', __name__)

@node_bp.route('/bids/<int:bid_id>/nodes', methods=['GET'])
@jwt_required()
def get_bid_nodes(bid_id):
    """获取标书的节点树结构"""
    user_id = get_jwt_identity()
    
    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404
    
    # 获取根节点
    root_node = bid.get_root_node()
    if not root_node:
        # 如果没有根节点，创建一个
        root_node = bid.create_root_node(user_id)
        db.session.commit()
    
    return jsonify({
        'bid_id': bid_id,
        'root_node': root_node.to_dict(include_children=True)
    })

@node_bp.route('/bids/<int:bid_id>/nodes', methods=['POST'])
@jwt_required()
def create_node(bid_id):
    """创建新节点"""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404
    
    # 验证必需字段
    if not data.get('node_type'):
        return jsonify({'error': '节点类型不能为空'}), 400
    
    try:
        # 创建新节点
        new_node = BidNode(
            node_type=data['node_type'],
            content=json.dumps(data.get('content')) if data.get('content') else None,
            node_metadata=json.dumps(data.get('metadata')) if data.get('metadata') else None,
            order_index=data.get('order_index', 0),
            bid_id=bid_id
        )
        db.session.add(new_node)
        db.session.flush()
        
        # 如果指定了父节点，创建关联
        parent_node_id = data.get('parent_node_id')
        if parent_node_id:
            parent_node = BidNode.query.filter_by(id=parent_node_id, bid_id=bid_id).first()
            if not parent_node:
                return jsonify({'error': '父节点不存在'}), 404
            
            parent_node.add_child(new_node, user_id)
        
        db.session.commit()
        
        return jsonify({
            'message': '节点创建成功',
            'node': new_node.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建节点失败: {str(e)}'}), 500

@node_bp.route('/nodes/<node_id>', methods=['PUT'])
@jwt_required()
def update_node(node_id):
    """更新节点内容"""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    # 查找节点
    node = BidNode.query.get(node_id)
    if not node:
        return jsonify({'error': '节点不存在'}), 404
    
    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == node.bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '无权限访问此节点'}), 403
    
    try:
        # 使用增量式更新
        new_node = node.update_content(
            new_content=data.get('content'),
            new_metadata=data.get('metadata'),
            user_id=user_id
        )
        
        db.session.commit()
        
        return jsonify({
            'message': '节点更新成功',
            'node': new_node.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新节点失败: {str(e)}'}), 500

@node_bp.route('/nodes/<node_id>', methods=['DELETE'])
@jwt_required()
def delete_node(node_id):
    """删除节点"""
    user_id = get_jwt_identity()
    
    # 查找节点
    node = BidNode.query.get(node_id)
    if not node:
        return jsonify({'error': '节点不存在'}), 404
    
    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == node.bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '无权限访问此节点'}), 403
    
    # 不能删除根节点
    root_node = bid.get_root_node()
    if root_node and root_node.id == node_id:
        return jsonify({'error': '不能删除根节点'}), 400
    
    try:
        # 记录删除历史
        if node.parent_intermediate:
            history = BidHistory(
                action_type='delete',
                description=f'删除节点: {node.node_type}',
                old_child_node_id=node_id,
                intermediate_id=node.parent_intermediate.id,
                bid_id=node.bid_id,
                user_id=user_id
            )
            db.session.add(history)
            
            # 从中间表中移除引用
            node.parent_intermediate.child_node_id = None
        
        db.session.commit()
        
        return jsonify({'message': '节点删除成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除节点失败: {str(e)}'}), 500

@node_bp.route('/nodes/<node_id>/move', methods=['POST'])
@jwt_required()
def move_node(node_id):
    """移动节点到新的父节点"""
    user_id = get_jwt_identity()
    data = request.get_json()
    
    # 查找节点
    node = BidNode.query.get(node_id)
    if not node:
        return jsonify({'error': '节点不存在'}), 404
    
    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == node.bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '无权限访问此节点'}), 403
    
    new_parent_id = data.get('new_parent_id')
    new_order_index = data.get('new_order_index', 0)
    
    if not new_parent_id:
        return jsonify({'error': '新父节点ID不能为空'}), 400
    
    # 查找新父节点
    new_parent = BidNode.query.filter_by(id=new_parent_id, bid_id=node.bid_id).first()
    if not new_parent:
        return jsonify({'error': '新父节点不存在'}), 404
    
    try:
        # 记录移动历史
        old_intermediate = node.parent_intermediate
        if old_intermediate:
            history = BidHistory(
                action_type='move',
                description=f'移动节点: {node.node_type}',
                old_child_node_id=node_id,
                intermediate_id=old_intermediate.id,
                bid_id=node.bid_id,
                user_id=user_id
            )
            db.session.add(history)
            
            # 从旧父节点移除
            old_intermediate.child_node_id = None
        
        # 更新节点顺序
        node.order_index = new_order_index
        
        # 添加到新父节点
        new_parent.add_child(node, user_id)
        
        db.session.commit()
        
        return jsonify({'message': '节点移动成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'移动节点失败: {str(e)}'}), 500

@node_bp.route('/nodes/<node_id>', methods=['GET'])
@jwt_required()
def get_node(node_id):
    """获取单个节点详情"""
    user_id = get_jwt_identity()
    
    # 查找节点
    node = BidNode.query.get(node_id)
    if not node:
        return jsonify({'error': '节点不存在'}), 404
    
    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == node.bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '无权限访问此节点'}), 403
    
    return jsonify({
        'node': node.to_dict(include_children=True)
    })

@node_bp.route('/bids/<int:bid_id>/history', methods=['GET'])
@jwt_required()
def get_bid_history(bid_id):
    """获取标书的编辑历史"""
    user_id = get_jwt_identity()

    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404

    limit = request.args.get('limit', 50, type=int)
    histories = BidHistory.get_bid_history(bid_id, limit)

    return jsonify({
        'bid_id': bid_id,
        'histories': [history.to_dict() for history in histories]
    })

@node_bp.route('/bids/<int:bid_id>/undo', methods=['POST'])
@jwt_required()
def undo_last_action(bid_id):
    """撤销最后一个操作"""
    user_id = get_jwt_identity()

    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404

    try:
        undone_history = BidHistory.undo_last_action(bid_id, user_id)

        if undone_history:
            return jsonify({
                'message': '撤销操作成功',
                'undone_action': undone_history.to_dict()
            })
        else:
            return jsonify({'error': '没有可撤销的操作'}), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'撤销操作失败: {str(e)}'}), 500

@node_bp.route('/history/<history_id>/undo', methods=['POST'])
@jwt_required()
def undo_specific_action(history_id):
    """撤销特定的历史操作"""
    user_id = get_jwt_identity()

    # 查找历史记录
    history = BidHistory.query.get(history_id)
    if not history:
        return jsonify({'error': '历史记录不存在'}), 404

    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == history.bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '无权限访问此历史记录'}), 403

    try:
        if history.undo():
            db.session.commit()
            return jsonify({
                'message': '撤销操作成功',
                'undone_action': history.to_dict()
            })
        else:
            return jsonify({'error': '无法撤销此操作'}), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'撤销操作失败: {str(e)}'}), 500

@node_bp.route('/history/<history_id>/redo', methods=['POST'])
@jwt_required()
def redo_specific_action(history_id):
    """重做特定的历史操作"""
    user_id = get_jwt_identity()

    # 查找历史记录
    history = BidHistory.query.get(history_id)
    if not history:
        return jsonify({'error': '历史记录不存在'}), 404

    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == history.bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '无权限访问此历史记录'}), 403

    try:
        if history.redo():
            db.session.commit()
            return jsonify({
                'message': '重做操作成功',
                'redone_action': history.to_dict()
            })
        else:
            return jsonify({'error': '无法重做此操作'}), 400

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'重做操作失败: {str(e)}'}), 500

@node_bp.route('/bids/<int:bid_id>/nodes/batch', methods=['POST'])
@jwt_required()
def batch_update_nodes(bid_id):
    """批量更新节点"""
    user_id = get_jwt_identity()
    data = request.get_json()

    # 验证用户权限 - 通过公司关联检查
    bid = Bid.query.join(Company).filter(
        Bid.id == bid_id,
        Company.user_id == user_id
    ).first()
    if not bid:
        return jsonify({'error': '标书不存在或无权限访问'}), 404

    operations = data.get('operations', [])
    if not operations:
        return jsonify({'error': '操作列表不能为空'}), 400

    try:
        results = []

        for operation in operations:
            op_type = operation.get('type')
            node_id = operation.get('node_id')
            op_data = operation.get('data', {})

            if op_type == 'update' and node_id:
                node = BidNode.query.filter_by(id=node_id, bid_id=bid_id).first()
                if node:
                    new_node = node.update_content(
                        new_content=op_data.get('content'),
                        new_metadata=op_data.get('metadata'),
                        user_id=user_id
                    )
                    results.append({
                        'operation': operation,
                        'result': 'success',
                        'new_node': new_node.to_dict()
                    })
                else:
                    results.append({
                        'operation': operation,
                        'result': 'error',
                        'error': '节点不存在'
                    })

            elif op_type == 'create':
                new_node = BidNode(
                    node_type=op_data['node_type'],
                    content=json.dumps(op_data.get('content')) if op_data.get('content') else None,
                    node_metadata=json.dumps(op_data.get('metadata')) if op_data.get('metadata') else None,
                    order_index=op_data.get('order_index', 0),
                    bid_id=bid_id
                )
                db.session.add(new_node)
                db.session.flush()

                parent_node_id = op_data.get('parent_node_id')
                if parent_node_id:
                    parent_node = BidNode.query.filter_by(id=parent_node_id, bid_id=bid_id).first()
                    if parent_node:
                        parent_node.add_child(new_node, user_id)

                results.append({
                    'operation': operation,
                    'result': 'success',
                    'new_node': new_node.to_dict()
                })

            else:
                results.append({
                    'operation': operation,
                    'result': 'error',
                    'error': '不支持的操作类型'
                })

        db.session.commit()

        return jsonify({
            'message': '批量操作完成',
            'results': results
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'批量操作失败: {str(e)}'}), 500
