"""
文档解析工具模块
支持解析 PDF、DOC、DOCX 文件内容
"""

import os
import uuid
import json
from typing import Dict, List, Any, Optional
import magic
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import PyPDF2
import logging

logger = logging.getLogger(__name__)


class DocumentParser:
    """文档解析器"""
    
    def __init__(self):
        self.supported_types = {
            'application/pdf': self._parse_pdf,
            'application/msword': self._parse_doc,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._parse_docx
        }
    
    def parse_file(self, file_path: str) -> Dict[str, Any]:
        """
        解析文件并返回结构化内容

        Args:
            file_path: 文件路径

        Returns:
            解析结果字典，包含：
            - content: 文本内容
            - nodes: 节点结构列表
            - metadata: 元数据信息
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 检测文件类型
        mime_type = magic.from_file(file_path, mime=True)
        logger.info(f"检测到文件类型: {mime_type}")

        # 尝试根据MIME类型解析
        if mime_type in self.supported_types:
            parser_func = self.supported_types[mime_type]
            return parser_func(file_path)

        # 如果MIME类型不匹配，尝试根据文件扩展名解析
        file_extension = os.path.splitext(file_path)[1].lower()
        logger.info(f"文件扩展名: {file_extension}")

        if file_extension == '.pdf':
            logger.warning(f"PDF文件MIME类型不匹配({mime_type})，但根据扩展名尝试解析")
            return self._parse_pdf(file_path)
        elif file_extension == '.docx':
            logger.warning(f"DOCX文件MIME类型不匹配({mime_type})，但根据扩展名尝试解析")
            return self._parse_docx(file_path)
        elif file_extension == '.doc':
            logger.warning(f"DOC文件MIME类型不匹配({mime_type})，但根据扩展名尝试解析")
            return self._parse_doc(file_path)

        raise ValueError(f"不支持的文件类型: {mime_type} (扩展名: {file_extension})")
    
    def _parse_pdf(self, file_path: str) -> Dict[str, Any]:
        """解析PDF文件"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # 提取文本内容
                text_content = ""
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    text_content += f"\n--- 第 {page_num + 1} 页 ---\n"
                    text_content += page_text
                
                # 创建节点结构
                nodes = self._create_nodes_from_text(text_content)
                
                # 元数据
                metadata = {
                    'page_count': len(pdf_reader.pages),
                    'word_count': len(text_content.split()),
                    'char_count': len(text_content),
                    'file_type': 'pdf',
                    'has_images': False,  # PDF图片提取需要更复杂的处理
                    'has_tables': False   # PDF表格识别需要更复杂的处理
                }
                
                return {
                    'content': text_content.strip(),
                    'nodes': nodes,
                    'metadata': metadata,
                    'preview': text_content[:500] + "..." if len(text_content) > 500 else text_content
                }
                
        except Exception as e:
            logger.error(f"PDF解析失败: {str(e)}")
            raise ValueError(f"PDF文件解析失败: {str(e)}")
    
    def _parse_docx(self, file_path: str) -> Dict[str, Any]:
        """解析DOCX文件"""
        try:
            doc = Document(file_path)
            
            # 提取文本内容和结构
            text_content = ""
            nodes = []
            has_images = False
            has_tables = False
            
            for element in doc.element.body:
                if element.tag.endswith('p'):  # 段落
                    para = None
                    for p in doc.paragraphs:
                        if p._element == element:
                            para = p
                            break
                    
                    if para and para.text.strip():
                        text_content += para.text + "\n"
                        
                        # 创建文本节点
                        node = {
                            'id': str(uuid.uuid4()),
                            'type': 'text',
                            'content': para.text.strip(),
                            'metadata': {
                                'alignment': self._get_alignment_name(para.alignment),
                                'style': para.style.name if para.style else 'Normal'
                            }
                        }
                        nodes.append(node)
                
                elif element.tag.endswith('tbl'):  # 表格
                    has_tables = True
                    table = None
                    for t in doc.tables:
                        if t._element == element:
                            table = t
                            break
                    
                    if table:
                        # 提取表格数据
                        table_data = []
                        for row in table.rows:
                            row_data = []
                            for cell in row.cells:
                                row_data.append(cell.text.strip())
                            table_data.append(row_data)
                        
                        # 创建表格节点
                        node = {
                            'id': str(uuid.uuid4()),
                            'type': 'table',
                            'content': json.dumps(table_data, ensure_ascii=False),
                            'metadata': {
                                'rows': len(table_data),
                                'cols': len(table_data[0]) if table_data else 0
                            }
                        }
                        nodes.append(node)
                        
                        # 添加到文本内容
                        text_content += f"\n[表格 {len(table_data)}行 x {len(table_data[0]) if table_data else 0}列]\n"
            
            # 检查是否有图片
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    has_images = True
                    break
            
            # 元数据
            metadata = {
                'page_count': 1,  # DOCX页数计算比较复杂，暂时设为1
                'word_count': len(text_content.split()),
                'char_count': len(text_content),
                'file_type': 'docx',
                'has_images': has_images,
                'has_tables': has_tables,
                'paragraph_count': len([p for p in doc.paragraphs if p.text.strip()]),
                'table_count': len(doc.tables)
            }
            
            return {
                'content': text_content.strip(),
                'nodes': nodes,
                'metadata': metadata,
                'preview': text_content[:500] + "..." if len(text_content) > 500 else text_content
            }
            
        except Exception as e:
            logger.error(f"DOCX解析失败: {str(e)}")
            raise ValueError(f"DOCX文件解析失败: {str(e)}")
    
    def _parse_doc(self, file_path: str) -> Dict[str, Any]:
        """解析DOC文件（旧版Word格式）"""
        # DOC格式解析比较复杂，这里提供一个基础实现
        # 实际项目中可能需要使用 python-docx2txt 或其他专门的库
        try:
            # 暂时返回一个基础的解析结果
            # 实际实现中可以使用 subprocess 调用 antiword 或其他工具
            text_content = "DOC文件解析功能正在开发中，请使用DOCX格式。"
            
            nodes = [{
                'id': str(uuid.uuid4()),
                'type': 'text',
                'content': text_content,
                'metadata': {}
            }]
            
            metadata = {
                'page_count': 1,
                'word_count': len(text_content.split()),
                'char_count': len(text_content),
                'file_type': 'doc',
                'has_images': False,
                'has_tables': False
            }
            
            return {
                'content': text_content,
                'nodes': nodes,
                'metadata': metadata,
                'preview': text_content
            }
            
        except Exception as e:
            logger.error(f"DOC解析失败: {str(e)}")
            raise ValueError(f"DOC文件解析失败: {str(e)}")
    
    def _create_nodes_from_text(self, text: str) -> List[Dict[str, Any]]:
        """从纯文本创建节点结构"""
        nodes = []
        
        # 按段落分割文本
        paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
        
        for para in paragraphs:
            if para:
                node = {
                    'id': str(uuid.uuid4()),
                    'type': 'text',
                    'content': para,
                    'metadata': {}
                }
                nodes.append(node)
        
        return nodes
    
    def _get_alignment_name(self, alignment) -> str:
        """获取对齐方式名称"""
        if alignment is None:
            return 'left'
        
        alignment_map = {
            WD_PARAGRAPH_ALIGNMENT.LEFT: 'left',
            WD_PARAGRAPH_ALIGNMENT.CENTER: 'center',
            WD_PARAGRAPH_ALIGNMENT.RIGHT: 'right',
            WD_PARAGRAPH_ALIGNMENT.JUSTIFY: 'justify'
        }
        
        return alignment_map.get(alignment, 'left')


def create_bid_from_parsed_content(parsed_content: Dict[str, Any], title: str, 
                                 description: str = "", user_id: int = None, 
                                 company_id: int = None) -> Dict[str, Any]:
    """
    从解析的内容创建标书数据结构
    
    Args:
        parsed_content: 解析的文档内容
        title: 标书标题
        description: 标书描述
        user_id: 用户ID
        company_id: 公司ID
        
    Returns:
        标书数据字典
    """
    
    # 创建根节点ID
    root_node_id = str(uuid.uuid4())
    
    # 准备标书数据
    bid_data = {
        'title': title,
        'description': description,
        'content': parsed_content.get('content', ''),
        'status': 'draft',
        'root_node_id': root_node_id,
        'company_id': company_id
    }
    
    # 准备节点数据
    nodes_data = []
    
    # 创建根容器节点
    root_node = {
        'id': root_node_id,
        'node_type': 'container',
        'content': json.dumps({'title': '导入的标书内容'}, ensure_ascii=False),
        'node_metadata': json.dumps({'imported': True}, ensure_ascii=False),
        'order_index': 0
    }
    nodes_data.append(root_node)
    
    # 添加解析出的节点
    for i, node in enumerate(parsed_content.get('nodes', [])):
        node_data = {
            'id': node['id'],
            'node_type': node['type'],
            'content': json.dumps({'text': node['content']}, ensure_ascii=False) if node['type'] == 'text' else node['content'],
            'node_metadata': json.dumps(node.get('metadata', {}), ensure_ascii=False),
            'order_index': i + 1
        }
        nodes_data.append(node_data)
    
    return {
        'bid': bid_data,
        'nodes': nodes_data,
        'metadata': parsed_content.get('metadata', {})
    }
