# -*- coding: utf-8 -*-
"""
数据模型测试
"""

import pytest
import json
from datetime import datetime
from app import db
from app.models import (
    User, Company, Bid, TenderDocument, FileStorage,
    CompanyInformationType, TenderInformationType,
    CompanyInformation, TenderInformation, BidReference,
    BidNode, BidIntermediate, BidHistory
)


class TestUserModel:
    """用户模型测试"""
    
    def test_create_user(self, app):
        """测试创建用户"""
        with app.app_context():
            user = User(
                username='testuser',
                phone='13800138000',
                email='<EMAIL>'
            )
            db.session.add(user)
            db.session.commit()
            
            assert user.id is not None
            assert user.username == 'testuser'
            assert user.phone == '13800138000'
            assert user.email == '<EMAIL>'
            assert user.is_active is True
            assert user.created_at is not None
    
    def test_user_to_dict(self, app, test_user):
        """测试用户序列化"""
        user = test_user()  # 调用函数获取用户对象
        user_dict = user.to_dict()

        assert 'id' in user_dict
        assert 'username' in user_dict
        assert 'phone' in user_dict
        assert 'email' in user_dict
        assert 'is_active' in user_dict
        assert 'created_at' in user_dict
        assert 'updated_at' in user_dict
        # 敏感信息不应该被序列化
        assert 'password_hash' not in user_dict
    
    def test_user_relationships(self, app, test_user, test_company):
        """测试用户关系"""
        with app.app_context():
            user = db.session.get(User, test_user.id)
            # 测试公司关系（现在是直接关联）
            assert len(user.companies) >= 0  # 可能没有公司


class TestCompanyModel:
    """公司模型测试"""
    
    def test_create_company(self, app):
        """测试创建公司"""
        with app.app_context():
            # 先创建用户
            user = User(
                username='testuser',
                phone='13800138000',
                email='<EMAIL>'
            )
            db.session.add(user)
            db.session.flush()  # 获取user.id

            company = Company(
                name='测试公司',
                unified_social_credit_code='91110000000000000X',
                legal_representative='张三',
                registered_capital='100万',
                address='北京市朝阳区',
                user_id=user.id
            )
            db.session.add(company)
            db.session.commit()

            assert company.id is not None
            assert company.name == '测试公司'
            assert company.unified_social_credit_code == '91110000000000000X'
            assert company.user_id == user.id
            assert company.is_verified is False
            assert company.created_at is not None
    
    def test_company_to_dict(self, app, test_company):
        """测试公司序列化"""
        company = test_company()  # 调用函数获取公司对象
        company_dict = company.to_dict()

        assert 'id' in company_dict
        assert 'name' in company_dict
        assert 'unified_social_credit_code' in company_dict
        assert 'legal_representative' in company_dict
        assert 'is_verified' in company_dict
        assert 'created_at' in company_dict


class TestBidModel:
    """标书模型测试"""
    
    def test_create_bid(self, app, test_user, test_company):
        """测试创建标书"""
        with app.app_context():
            bid = Bid(
                title='测试标书',
                description='测试描述',
                content='测试内容',
                status='draft',
                project_budget='100万',
                company_id=test_company.id  # test_company有.id属性
            )
            db.session.add(bid)
            db.session.commit()

            assert bid.id is not None
            assert bid.title == '测试标书'
            assert bid.status == 'draft'
            assert bid.company_id == test_company.id
            assert bid.created_at is not None
    
    def test_bid_to_dict(self, app, test_bid):
        """测试标书序列化"""
        bid = test_bid()  # 调用函数获取标书对象
        bid_dict = bid.to_dict()

        assert 'id' in bid_dict
        assert 'title' in bid_dict
        assert 'description' in bid_dict
        assert 'status' in bid_dict
        assert 'company_id' in bid_dict  # 移除user_id检查，因为已经删除
        assert 'created_at' in bid_dict

    def test_bid_relationships(self, app, test_bid):
        """测试标书关系"""
        with app.app_context():
            bid = db.session.get(Bid, test_bid.id)
            assert bid.company is not None  # 移除user关系检查，因为已经删除
            assert bid.company.name == '测试公司'
            assert bid.company.name == '测试公司'


class TestTenderDocumentModel:
    """招标文件模型测试"""
    
    def test_create_tender_document(self, app, test_user):
        """测试创建招标文件"""
        with app.app_context():
            # 先创建文件存储记录
            file_storage = FileStorage(
                file_hash='abc123def456',
                original_filename='招标文件.pdf',
                file_size=1024,
                file_type='pdf',
                mime_type='application/pdf'
            )
            db.session.add(file_storage)
            db.session.flush()  # 获取file_storage.id

            # 创建招标文件
            doc = TenderDocument(
                title='测试招标文件',
                file_id=file_storage.id
            )
            db.session.add(doc)
            db.session.commit()

            assert doc.id is not None
            assert doc.title == '测试招标文件'
            assert doc.file_id == file_storage.id
            assert doc.file.original_filename == '招标文件.pdf'

    def test_tender_document_to_dict(self, app, test_tender_doc):
        """测试招标文件序列化"""
        with app.app_context():
            # 重新从数据库获取对象以确保session绑定
            doc = db.session.get(TenderDocument, test_tender_doc.id)
            doc_dict = doc.to_dict()

            assert 'id' in doc_dict
            assert 'title' in doc_dict
            assert 'file_id' in doc_dict
            assert 'filename' in doc_dict  # 这个来自关联的file.original_filename
            assert 'file_size' in doc_dict
            assert 'file_type' in doc_dict
            assert 'created_at' in doc_dict





class TestBidNodeModel:
    """标书节点模型测试"""

    def test_create_bid_node(self, app, test_bid):
        """测试创建标书节点"""
        with app.app_context():
            node = BidNode(
                node_type='text',
                content=json.dumps({'text': '测试文本', 'format': 'plain'}),
                node_metadata=json.dumps({'font_size': 14, 'color': '#000000'}),
                order_index=0,
                bid_id=test_bid.id
            )
            db.session.add(node)
            db.session.commit()

            assert node.id is not None
            assert node.node_type == 'text'
            assert node.order_index == 0
            assert node.bid_id == test_bid.id
            assert node.created_at is not None

    def test_bid_node_to_dict(self, app, test_bid):
        """测试节点序列化"""
        with app.app_context():
            node = BidNode(
                node_type='text',
                content=json.dumps({'text': '测试文本'}),
                node_metadata=json.dumps({'font_size': 14}),
                bid_id=test_bid.id
            )
            db.session.add(node)
            db.session.commit()

            node_dict = node.to_dict()

            assert 'id' in node_dict
            assert 'node_type' in node_dict
            assert 'content' in node_dict
            assert 'metadata' in node_dict
            assert node_dict['content']['text'] == '测试文本'
            assert node_dict['metadata']['font_size'] == 14

    def test_bid_node_children(self, app, test_bid):
        """测试节点子节点关系"""
        with app.app_context():
            # 创建父节点
            parent_node = BidNode(
                node_type='container',
                content=json.dumps({'title': '父容器'}),
                bid_id=test_bid.id,
                order_index=0
            )
            db.session.add(parent_node)
            db.session.flush()

            # 创建子节点
            child_node = BidNode(
                node_type='text',
                content=json.dumps({'text': '子文本'}),
                bid_id=test_bid.id,
                order_index=0
            )
            db.session.add(child_node)
            db.session.flush()

            # 创建中间表关联
            intermediate = BidIntermediate(
                parent_node_id=parent_node.id,
                child_node_id=child_node.id
            )
            db.session.add(intermediate)
            child_node.parent_intermediate_id = intermediate.id
            db.session.commit()

            # 测试关系
            children = parent_node.get_children()
            assert len(children) == 1
            assert children[0].id == child_node.id

    def test_update_node_content(self, app, test_bid, test_user):
        """测试增量式更新节点内容"""
        with app.app_context():
            # 创建原始节点
            original_node = BidNode(
                node_type='text',
                content=json.dumps({'text': '原始文本'}),
                bid_id=test_bid.id
            )
            db.session.add(original_node)
            db.session.commit()

            # 更新内容
            new_content = {'text': '更新后的文本', 'format': 'markdown'}
            new_metadata = {'font_size': 16, 'bold': True}

            new_node = original_node.update_content(
                new_content=new_content,
                new_metadata=new_metadata,
                user_id=test_user.id
            )
            db.session.commit()

            assert new_node.id != original_node.id  # 应该是新节点
            assert json.loads(new_node.content)['text'] == '更新后的文本'
            assert json.loads(new_node.node_metadata)['font_size'] == 16


class TestBidIntermediateModel:
    """标书中间表模型测试"""

    def test_create_intermediate(self, app, test_bid):
        """测试创建中间表记录"""
        with app.app_context():
            # 创建两个节点
            parent_node = BidNode(
                node_type='container',
                content=json.dumps({'title': '父节点'}),
                bid_id=test_bid.id
            )
            child_node = BidNode(
                node_type='text',
                content=json.dumps({'text': '子节点'}),
                bid_id=test_bid.id
            )
            db.session.add_all([parent_node, child_node])
            db.session.flush()

            # 创建中间表记录
            intermediate = BidIntermediate(
                parent_node_id=parent_node.id,
                child_node_id=child_node.id
            )
            db.session.add(intermediate)
            db.session.commit()

            assert intermediate.id is not None
            assert intermediate.parent_node_id == parent_node.id
            assert intermediate.child_node_id == child_node.id
            assert intermediate.created_at is not None

    def test_intermediate_to_dict(self, app, test_bid):
        """测试中间表序列化"""
        with app.app_context():
            parent_node = BidNode(node_type='container', bid_id=test_bid.id)
            child_node = BidNode(node_type='text', bid_id=test_bid.id)
            db.session.add_all([parent_node, child_node])
            db.session.flush()

            intermediate = BidIntermediate(
                parent_node_id=parent_node.id,
                child_node_id=child_node.id
            )
            db.session.add(intermediate)
            db.session.commit()

            intermediate_dict = intermediate.to_dict()

            assert 'id' in intermediate_dict
            assert 'parent_node_id' in intermediate_dict
            assert 'child_node_id' in intermediate_dict
            assert 'created_at' in intermediate_dict


class TestBidHistoryModel:
    """标书历史记录模型测试"""

    def test_create_history(self, app, test_bid, test_user):
        """测试创建历史记录"""
        with app.app_context():
            # 创建节点和中间表
            node = BidNode(node_type='text', bid_id=test_bid.id)
            db.session.add(node)
            db.session.flush()

            intermediate = BidIntermediate(
                parent_node_id=node.id,
                child_node_id=node.id
            )
            db.session.add(intermediate)
            db.session.flush()

            # 创建历史记录
            history = BidHistory(
                action_type='create',
                description='创建文本节点',
                new_child_node_id=node.id,
                intermediate_id=intermediate.id,
                bid_id=test_bid.id,
                user_id=test_user.id
            )
            db.session.add(history)
            db.session.commit()

            assert history.id is not None
            assert history.action_type == 'create'
            assert history.description == '创建文本节点'
            assert history.bid_id == test_bid.id
            assert history.user_id == test_user.id

    def test_get_bid_history(self, app, test_bid, test_user):
        """测试获取标书历史"""
        with app.app_context():
            # 创建多个历史记录
            for i in range(3):
                node = BidNode(node_type='text', bid_id=test_bid.id)
                db.session.add(node)
                db.session.flush()

                intermediate = BidIntermediate(
                    parent_node_id=node.id,
                    child_node_id=node.id
                )
                db.session.add(intermediate)
                db.session.flush()

                history = BidHistory(
                    action_type='create',
                    description=f'创建节点 {i}',
                    new_child_node_id=node.id,
                    intermediate_id=intermediate.id,
                    bid_id=test_bid.id,
                    user_id=test_user.id
                )
                db.session.add(history)

            db.session.commit()

            # 获取历史记录
            histories = BidHistory.get_bid_history(test_bid.id, limit=10)
            assert len(histories) == 3
            # 应该按时间倒序排列
            assert histories[0].description == '创建节点 2'
            assert histories[1].description == '创建节点 1'
            assert histories[2].description == '创建节点 0'


class TestBidRootNode:
    """标书根节点测试"""

    def test_create_root_node(self, app, test_bid, test_user):
        """测试创建根节点"""
        with app.app_context():
            bid = db.session.get(Bid, test_bid.id)

            # 初始时没有根节点
            assert bid.get_root_node() is None

            # 创建根节点
            root_node = bid.create_root_node(test_user.id)
            db.session.commit()

            assert root_node is not None
            assert root_node.node_type == 'container'
            assert bid.root_intermediate_id is not None

            # 验证中间表记录
            intermediate = BidIntermediate.query.get(bid.root_intermediate_id)
            assert intermediate is not None
            assert intermediate.child_node_id == root_node.id
            assert intermediate.parent_node_id is None  # 根节点没有父节点

            # 再次调用应该返回同一个根节点
            same_root = bid.create_root_node(test_user.id)
            assert same_root.id == root_node.id

    def test_get_root_node(self, app, test_bid, test_user):
        """测试获取根节点"""
        with app.app_context():
            bid = db.session.get(Bid, test_bid.id)

            # 创建根节点
            root_node = bid.create_root_node(test_user.id)
            db.session.commit()

            # 获取根节点
            retrieved_root = bid.get_root_node()
            assert retrieved_root is not None
            assert retrieved_root.id == root_node.id


class TestFileStorageModel:
    """文件存储模型测试"""

    def test_create_file_storage(self, app):
        """测试创建文件存储记录"""
        with app.app_context():
            file_storage = FileStorage(
                file_hash='abc123def456',
                original_filename='test.pdf',
                file_size=1024,
                file_type='pdf',
                mime_type='application/pdf'
            )
            db.session.add(file_storage)
            db.session.commit()

            assert file_storage.id is not None
            assert file_storage.file_hash == 'abc123def456'
            assert file_storage.original_filename == 'test.pdf'
            assert file_storage.file_size == 1024
            assert file_storage.file_type == 'pdf'

    def test_file_path_property(self, app):
        """测试文件路径属性"""
        with app.app_context():
            file_storage = FileStorage(
                file_hash='abc123',
                original_filename='test.pdf',
                file_size=1024,
                file_type='pdf'
            )
            expected_path = 'uploads/abc123/test.pdf'
            assert file_storage.file_path == expected_path

    def test_calculate_file_hash(self):
        """测试文件哈希计算"""
        content = b'test content'
        hash_value = FileStorage.calculate_file_hash(content)
        assert len(hash_value) == 64  # SHA256 hash length


class TestCompanyInformationTypeModel:
    """公司信息类型模型测试"""

    def test_create_company_info_type(self, app):
        """测试创建公司信息类型"""
        with app.app_context():
            info_type = CompanyInformationType(
                name='公司简介',
                description='公司基本介绍',
                category='基本信息',
                data_type='text',
                is_required=True,
                sort_order=1
            )
            db.session.add(info_type)
            db.session.commit()

            assert info_type.id is not None
            assert info_type.name == '公司简介'
            assert info_type.category == '基本信息'
            assert info_type.is_required is True


class TestTenderInformationTypeModel:
    """招标文件信息类型模型测试"""

    def test_create_tender_info_type(self, app):
        """测试创建招标文件信息类型"""
        with app.app_context():
            info_type = TenderInformationType(
                name='项目名称',
                description='招标项目名称',
                category='项目信息',
                data_type='text',
                is_required=False,
                sort_order=100
            )
            db.session.add(info_type)
            db.session.commit()

            assert info_type.id is not None
            assert info_type.name == '项目名称'
            assert info_type.category == '项目信息'
            assert info_type.is_required is False


class TestBidReferenceModel:
    """标书引用关系模型测试"""

    def test_create_bid_reference(self, app):
        """测试创建标书引用关系"""
        with app.app_context():
            # 创建用户和公司
            user = User(username='testuser', phone='13800138000')
            db.session.add(user)
            db.session.flush()

            company = Company(name='测试公司', user_id=user.id)
            db.session.add(company)
            db.session.flush()

            # 创建两个标书
            bid1 = Bid(title='标书1', company_id=company.id)
            bid2 = Bid(title='标书2', company_id=company.id)
            db.session.add_all([bid1, bid2])
            db.session.flush()

            # 创建引用关系
            reference = BidReference(
                source_bid_id=bid1.id,
                target_bid_id=bid2.id,
                reference_type='reference',
                description='参考标书'
            )
            db.session.add(reference)
            db.session.commit()

            assert reference.id is not None
            assert reference.source_bid_id == bid1.id
            assert reference.target_bid_id == bid2.id
            assert reference.reference_type == 'reference'
