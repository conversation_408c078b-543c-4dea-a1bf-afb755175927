# -*- coding: utf-8 -*-
"""
测试节点管理API
"""

import pytest
import json
from app.models import BidNode, BidIntermediate, BidHistory


class TestNodeRoutes:
    """节点路由测试"""

    def test_get_bid_nodes_empty(self, client, auth_headers, test_bid):
        """测试获取空标书的节点树"""
        response = client.get(
            f'/api/bids/{test_bid.id}/nodes',
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'bid_id' in data
        assert 'root_node' in data
        assert data['bid_id'] == test_bid.id
        assert data['root_node']['node_type'] == 'container'

    def test_create_text_node(self, client, auth_headers, test_bid):
        """测试创建文本节点"""
        node_data = {
            'node_type': 'text',
            'content': {
                'text': '这是一段测试文本',
                'format': 'plain'
            },
            'metadata': {
                'font_size': 14,
                'color': '#000000'
            },
            'order_index': 1
        }
        
        response = client.post(
            f'/api/bids/{test_bid.id}/nodes',
            headers=auth_headers,
            data=json.dumps(node_data)
        )
        
        assert response.status_code == 201
        data = response.get_json()
        assert 'node' in data
        assert data['node']['node_type'] == 'text'
        assert data['node']['content']['text'] == '这是一段测试文本'

    def test_create_node_with_parent(self, client, auth_headers, test_bid, test_user, app):
        """测试创建带父节点的节点"""
        # 首先获取根节点
        with app.app_context():
            bid = test_bid()
            root_node = bid.create_root_node(test_user.id)
            from app import db
            db.session.commit()
            root_node_id = root_node.id
        
        node_data = {
            'node_type': 'text',
            'content': {'text': '子节点文本'},
            'parent_node_id': root_node_id,
            'order_index': 0
        }
        
        response = client.post(
            f'/api/bids/{test_bid.id}/nodes',
            headers=auth_headers,
            data=json.dumps(node_data)
        )
        
        assert response.status_code == 201
        data = response.get_json()
        assert 'node' in data
        assert data['node']['node_type'] == 'text'

    def test_update_node_content(self, client, auth_headers, test_bid, app):
        """测试更新节点内容"""
        # 创建一个节点
        with app.app_context():
            from app import db
            node = BidNode(
                node_type='text',
                content=json.dumps({'text': '原始文本'}),
                bid_id=test_bid.id,
                order_index=0
            )
            db.session.add(node)
            db.session.commit()
            node_id = node.id
        
        update_data = {
            'content': {
                'text': '更新后的文本',
                'format': 'markdown'
            },
            'metadata': {
                'font_size': 16,
                'bold': True
            }
        }
        
        response = client.put(
            f'/api/nodes/{node_id}',
            headers=auth_headers,
            data=json.dumps(update_data)
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'node' in data
        assert data['node']['content']['text'] == '更新后的文本'

    def test_delete_node(self, client, auth_headers, test_bid, app):
        """测试删除节点"""
        # 创建父节点和子节点
        with app.app_context():
            from app import db
            parent_node = BidNode(
                node_type='container',
                content=json.dumps({'title': '父容器'}),
                bid_id=test_bid.id,
                order_index=0
            )
            db.session.add(parent_node)
            db.session.flush()
            
            child_node = BidNode(
                node_type='text',
                content=json.dumps({'text': '子文本'}),
                bid_id=test_bid.id,
                order_index=0
            )
            db.session.add(child_node)
            db.session.flush()
            
            # 创建中间表关联
            intermediate = BidIntermediate(
                parent_node_id=parent_node.id,
                child_node_id=child_node.id
            )
            db.session.add(intermediate)
            child_node.parent_intermediate_id = intermediate.id
            db.session.commit()
            
            child_node_id = child_node.id
        
        response = client.delete(
            f'/api/nodes/{child_node_id}',
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'message' in data

    def test_move_node(self, client, auth_headers, test_bid, app):
        """测试移动节点"""
        # 创建节点结构
        with app.app_context():
            from app import db
            
            # 创建两个父节点
            parent1 = BidNode(
                node_type='container',
                content=json.dumps({'title': '父容器1'}),
                bid_id=test_bid.id,
                order_index=0
            )
            parent2 = BidNode(
                node_type='container',
                content=json.dumps({'title': '父容器2'}),
                bid_id=test_bid.id,
                order_index=1
            )
            db.session.add_all([parent1, parent2])
            db.session.flush()
            
            # 创建子节点
            child_node = BidNode(
                node_type='text',
                content=json.dumps({'text': '要移动的文本'}),
                bid_id=test_bid.id,
                order_index=0
            )
            db.session.add(child_node)
            db.session.flush()
            
            # 将子节点关联到父容器1
            intermediate = BidIntermediate(
                parent_node_id=parent1.id,
                child_node_id=child_node.id
            )
            db.session.add(intermediate)
            child_node.parent_intermediate_id = intermediate.id
            db.session.commit()
            
            child_node_id = child_node.id
            parent2_id = parent2.id
        
        move_data = {
            'new_parent_id': parent2_id,
            'new_order_index': 0
        }
        
        response = client.post(
            f'/api/nodes/{child_node_id}/move',
            headers=auth_headers,
            data=json.dumps(move_data)
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'message' in data

    def test_get_node_details(self, client, auth_headers, test_bid, app):
        """测试获取节点详情"""
        with app.app_context():
            from app import db
            node = BidNode(
                node_type='text',
                content=json.dumps({'text': '测试文本'}),
                node_metadata=json.dumps({'font_size': 14}),
                bid_id=test_bid.id,
                order_index=0
            )
            db.session.add(node)
            db.session.commit()
            node_id = node.id
        
        response = client.get(
            f'/api/nodes/{node_id}',
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'node' in data
        assert data['node']['content']['text'] == '测试文本'
        assert data['node']['metadata']['font_size'] == 14

    def test_get_bid_history(self, client, auth_headers, test_bid, app):
        """测试获取标书历史"""
        # 创建一些历史记录
        with app.app_context():
            from app import db
            
            node = BidNode(
                node_type='text',
                content=json.dumps({'text': '测试'}),
                bid_id=test_bid.id
            )
            db.session.add(node)
            db.session.flush()
            
            intermediate = BidIntermediate(
                parent_node_id=node.id,
                child_node_id=node.id
            )
            db.session.add(intermediate)
            db.session.flush()
            
            history = BidHistory(
                action_type='create',
                description='创建测试节点',
                new_child_node_id=node.id,
                intermediate_id=intermediate.id,
                bid_id=test_bid.id,
                user_id=1  # 假设用户ID为1
            )
            db.session.add(history)
            db.session.commit()
        
        response = client.get(
            f'/api/bids/{test_bid.id}/history',
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert 'histories' in data
        assert len(data['histories']) > 0

    def test_unauthorized_access(self, client, test_bid):
        """测试未授权访问"""
        response = client.get(f'/api/bids/{test_bid.id}/nodes')
        assert response.status_code == 401

    def test_invalid_bid_access(self, client, auth_headers):
        """测试访问不存在的标书"""
        response = client.get(
            '/api/bids/99999/nodes',
            headers=auth_headers
        )
        assert response.status_code == 404

    def test_create_node_invalid_data(self, client, auth_headers, test_bid):
        """测试创建节点时提供无效数据"""
        invalid_data = {
            'content': {'text': '测试文本'}
            # 缺少必需的 node_type
        }
        
        response = client.post(
            f'/api/bids/{test_bid.id}/nodes',
            headers=auth_headers,
            data=json.dumps(invalid_data)
        )
        
        assert response.status_code == 400
        data = response.get_json()
        assert 'error' in data
