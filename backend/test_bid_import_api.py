#!/usr/bin/env python3
"""
测试bid导入API的脚本
"""

import requests
import json
import os
from io import BytesIO
from docx import Document

# API基础URL
BASE_URL = "http://localhost:5000/api"

def create_test_docx():
    """创建一个测试DOCX文件"""
    doc = Document()
    doc.add_heading('测试标书', 0)
    
    doc.add_heading('项目概述', level=1)
    doc.add_paragraph('这是一个测试项目的概述。我们将提供高质量的服务。')
    
    doc.add_heading('技术方案', level=1)
    doc.add_paragraph('我们的技术方案包括以下几个方面：')
    doc.add_paragraph('1. 系统架构设计')
    doc.add_paragraph('2. 数据库设计')
    doc.add_paragraph('3. 前端界面设计')
    
    # 添加表格
    table = doc.add_table(rows=1, cols=3)
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = '项目阶段'
    hdr_cells[1].text = '时间'
    hdr_cells[2].text = '负责人'
    
    row_cells = table.add_row().cells
    row_cells[0].text = '需求分析'
    row_cells[1].text = '2周'
    row_cells[2].text = '张三'
    
    row_cells = table.add_row().cells
    row_cells[0].text = '系统开发'
    row_cells[1].text = '8周'
    row_cells[2].text = '李四'
    
    # 保存到内存
    file_stream = BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    return file_stream

def test_login():
    """测试登录并获取token"""
    login_data = {
        "phone": "13800138000",
        "code": "123456"
    }

    response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    print(f"登录响应状态: {response.status_code}")
    print(f"登录响应内容: {response.text}")

    if response.status_code == 200:
        result = response.json()
        token = result.get('token')  # 修正字段名
        if token:
            print(f"登录成功，获取到token: {token[:20]}...")
            return token
        else:
            print(f"登录响应中没有token字段: {result}")
            return None
    else:
        print(f"登录失败: {response.text}")
        return None

def test_create_company(token):
    """创建测试公司或获取现有公司"""
    headers = {"Authorization": f"Bearer {token}"}

    # 先尝试获取现有公司
    response = requests.get(f"{BASE_URL}/companies/user", headers=headers)
    print(f"获取用户公司响应状态: {response.status_code}")

    if response.status_code == 200:
        result = response.json()
        print(f"获取公司响应: {result}")

        # 处理不同的响应格式
        if isinstance(result, list):
            companies = result
        else:
            companies = result.get('companies', [])

        if companies:
            company = companies[0]  # 使用第一个公司
            company_id = company.get('id')
            print(f"使用现有公司: {company.get('name')} (ID: {company_id})")
            return company_id

    # 如果没有现有公司，创建新公司
    import time
    company_data = {
        "name": f"测试公司-{int(time.time())}",  # 使用时间戳确保唯一性
        "description": "用于测试的公司"
    }

    response = requests.post(f"{BASE_URL}/companies", json=company_data, headers=headers)
    print(f"创建公司响应状态: {response.status_code}")

    if response.status_code == 201:
        result = response.json()
        company_id = result.get('company', {}).get('id')
        print(f"公司创建成功，ID: {company_id}")
        return company_id
    else:
        print(f"创建公司失败: {response.text}")
        return None

def test_bid_import(token, company_id):
    """测试标书导入"""
    headers = {"Authorization": f"Bearer {token}"}
    
    # 创建测试文件
    docx_file = create_test_docx()
    
    # 准备表单数据
    files = {
        'file': ('test_bid.docx', docx_file, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    }
    
    data = {
        'title': '测试导入标书',
        'description': '这是一个通过API导入的测试标书',
        'company_id': str(company_id)
    }
    
    print("开始测试标书导入...")
    response = requests.post(f"{BASE_URL}/bids/import", files=files, data=data, headers=headers)
    
    print(f"导入响应状态: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 201:
        result = response.json()
        print("标书导入成功!")
        print(f"标书ID: {result.get('bid', {}).get('id')}")
        print(f"标书标题: {result.get('bid', {}).get('title')}")
        return True
    else:
        print(f"标书导入失败: {response.text}")
        return False

def test_bid_import_without_company(token):
    """测试没有公司ID的情况"""
    headers = {"Authorization": f"Bearer {token}"}

    # 创建测试文件
    docx_file = create_test_docx()

    # 准备表单数据 - 故意不包含company_id
    files = {
        'file': ('test_bid.docx', docx_file, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    }

    data = {
        'title': '测试导入标书-无公司ID',
        'description': '这是一个测试400错误的请求'
        # 故意不包含 company_id
    }

    print("测试没有公司ID的导入请求...")
    response = requests.post(f"{BASE_URL}/bids/import", files=files, data=data, headers=headers)

    print(f"导入响应状态: {response.status_code}")
    print(f"响应内容: {response.text}")

    if response.status_code == 400:
        print("✅ 正确返回400错误")
        return True
    else:
        print("❌ 应该返回400错误但没有")
        return False

def main():
    """主函数"""
    print("开始测试bid导入API...")

    # 1. 登录获取token
    token = test_login()
    if not token:
        print("无法获取token，测试终止")
        return

    # 2. 创建公司
    company_id = test_create_company(token)
    if not company_id:
        print("无法创建公司，测试终止")
        return

    # 3. 测试标书导入
    success = test_bid_import(token, company_id)

    # 4. 测试400错误情况
    error_test_success = test_bid_import_without_company(token)

    if success and error_test_success:
        print("✅ 所有测试通过!")
    else:
        print("❌ 测试失败!")

if __name__ == "__main__":
    main()
