#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模拟API接口
验证公司信息查询、支付宝微信登录、导入招标文件、导入参考标书、生成标书等功能
"""

import requests
import json
import os
from io import BytesIO

# 配置
BASE_URL = "http://localhost:5000/api"
TEST_USER = {
    "username": "test_user",
    "password": "test123",
    "phone": "13800138000"
}

class MockAPITester:
    def __init__(self):
        self.token = None
        self.user_id = None
        self.company_id = None
        
    def register_and_login(self):
        """注册并登录测试用户"""
        print("=== 注册并登录测试用户 ===")
        
        # 注册
        register_data = {
            "username": TEST_USER["username"],
            "password": TEST_USER["password"],
            "phone": TEST_USER["phone"]
        }
        
        try:
            response = requests.post(f"{BASE_URL}/auth/register", json=register_data)
            if response.status_code in [200, 201]:
                print("✅ 用户注册成功")
            else:
                print(f"⚠️ 用户可能已存在: {response.json()}")
        except Exception as e:
            print(f"❌ 注册失败: {e}")
        
        # 登录
        login_data = {
            "username": TEST_USER["username"],
            "password": TEST_USER["password"]
        }
        
        try:
            response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("token")
                self.user_id = data.get("user", {}).get("id")
                print(f"✅ 登录成功，用户ID: {self.user_id}")
                return True
            else:
                print(f"❌ 登录失败: {response.json()}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_headers(self):
        """获取认证头"""
        return {"Authorization": f"Bearer {self.token}"}
    
    def test_company_search(self):
        """测试公司信息查询（模拟企查查API）"""
        print("\n=== 测试公司信息查询 ===")
        
        try:
            response = requests.get(
                f"{BASE_URL}/companies/search?name=测试公司",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                companies = response.json()
                print(f"✅ 公司搜索成功，返回 {len(companies)} 个结果")
                if companies:
                    print(f"   示例公司: {companies[0]['name']}")
                return True
            else:
                print(f"❌ 公司搜索失败: {response.json()}")
                return False
        except Exception as e:
            print(f"❌ 公司搜索异常: {e}")
            return False
    
    def test_wechat_login(self):
        """测试微信登录（模拟）"""
        print("\n=== 测试微信登录 ===")
        
        try:
            # 获取二维码
            response = requests.get(f"{BASE_URL}/auth/login/wechat/qr")
            if response.status_code == 200:
                qr_data = response.json()
                print(f"✅ 微信二维码获取成功: {qr_data['state']}")
                
                # 模拟登录
                login_data = {"code": "test_wechat_code"}
                response = requests.post(f"{BASE_URL}/auth/login/wechat", json=login_data)
                if response.status_code == 200:
                    print("✅ 微信登录模拟成功")
                    return True
                else:
                    print(f"❌ 微信登录失败: {response.json()}")
                    return False
            else:
                print(f"❌ 微信二维码获取失败: {response.json()}")
                return False
        except Exception as e:
            print(f"❌ 微信登录异常: {e}")
            return False
    
    def test_alipay_login(self):
        """测试支付宝登录（模拟）"""
        print("\n=== 测试支付宝登录 ===")
        
        try:
            # 获取二维码
            response = requests.get(f"{BASE_URL}/auth/login/alipay/qr")
            if response.status_code == 200:
                qr_data = response.json()
                print(f"✅ 支付宝二维码获取成功: {qr_data['state']}")
                
                # 模拟登录
                login_data = {"auth_code": "test_alipay_code"}
                response = requests.post(f"{BASE_URL}/auth/login/alipay", json=login_data)
                if response.status_code == 200:
                    print("✅ 支付宝登录模拟成功")
                    return True
                else:
                    print(f"❌ 支付宝登录失败: {response.json()}")
                    return False
            else:
                print(f"❌ 支付宝二维码获取失败: {response.json()}")
                return False
        except Exception as e:
            print(f"❌ 支付宝登录异常: {e}")
            return False
    
    def create_test_company(self):
        """创建测试公司"""
        print("\n=== 创建测试公司 ===")
        
        company_data = {
            "name": "测试科技有限公司",
            "unified_social_credit_code": "91110108MA01234567",
            "legal_representative": "张三",
            "registered_capital": "1000万元",
            "business_scope": "技术开发、技术推广、技术转让",
            "address": "北京市海淀区中关村大街1号",
            "contact_phone": "010-12345678",
            "contact_email": "<EMAIL>"
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/companies",
                json=company_data,
                headers=self.get_headers()
            )
            
            if response.status_code == 201:
                company = response.json()["company"]
                self.company_id = company["id"]
                print(f"✅ 公司创建成功，ID: {self.company_id}")
                return True
            else:
                print(f"❌ 公司创建失败: {response.json()}")
                return False
        except Exception as e:
            print(f"❌ 公司创建异常: {e}")
            return False
    
    def test_tender_document_upload(self):
        """测试招标文件上传（模拟解析）"""
        print("\n=== 测试招标文件上传 ===")
        
        # 创建模拟文件
        file_content = b"This is a mock tender document content for testing."
        files = {
            'file': ('test_tender.pdf', BytesIO(file_content), 'application/pdf')
        }
        data = {
            'title': '测试招标文件'
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/documents/tender",
                files=files,
                data=data,
                headers=self.get_headers()
            )
            
            if response.status_code == 201:
                result = response.json()
                print(f"✅ 招标文件上传成功: {result['document']['title']}")
                print(f"   解析信息: {result.get('parsed_info', {}).get('project_name', 'N/A')}")
                return result['document']['id']
            else:
                print(f"❌ 招标文件上传失败: {response.json()}")
                return None
        except Exception as e:
            print(f"❌ 招标文件上传异常: {e}")
            return None
    
    def test_bid_import(self):
        """测试标书导入（模拟内容）"""
        print("\n=== 测试标书导入 ===")
        
        if not self.company_id:
            print("❌ 需要先创建公司")
            return None
        
        # 创建模拟文件
        file_content = b"This is a mock bid document content for testing."
        files = {
            'file': ('test_bid.docx', BytesIO(file_content), 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        }
        data = {
            'title': '测试导入标书',
            'description': '这是一个测试导入的标书',
            'company_id': str(self.company_id)
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/bids/import",
                files=files,
                data=data,
                headers=self.get_headers()
            )
            
            if response.status_code == 201:
                result = response.json()
                print(f"✅ 标书导入成功: {result['bid']['title']}")
                print(f"   说明: {result.get('note', 'N/A')}")
                return result['bid']['id']
            else:
                print(f"❌ 标书导入失败: {response.json()}")
                return None
        except Exception as e:
            print(f"❌ 标书导入异常: {e}")
            return None
    
    def test_bid_generation(self, tender_doc_id):
        """测试标书生成（模拟AI生成）"""
        print("\n=== 测试标书生成 ===")
        
        if not self.company_id or not tender_doc_id:
            print("❌ 需要先创建公司和招标文件")
            return None
        
        generation_data = {
            "tender_document_ids": [tender_doc_id],
            "reference_bid_ids": [],
            "company_id": self.company_id
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/bids/generate",
                json=generation_data,
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 标书生成成功: {result['bid']['title']}")
                print(f"   内容长度: {len(result['bid']['content'])} 字符")
                return result['bid']['id']
            else:
                print(f"❌ 标书生成失败: {response.json()}")
                return None
        except Exception as e:
            print(f"❌ 标书生成异常: {e}")
            return None
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始测试模拟API接口...")
        
        # 登录
        if not self.register_and_login():
            print("❌ 登录失败，无法继续测试")
            return
        
        # 测试各个功能
        self.test_company_search()
        self.test_wechat_login()
        self.test_alipay_login()
        
        # 创建公司
        if not self.create_test_company():
            print("❌ 公司创建失败，跳过后续测试")
            return
        
        # 测试文档相关功能
        tender_doc_id = self.test_tender_document_upload()
        self.test_bid_import()
        
        if tender_doc_id:
            self.test_bid_generation(tender_doc_id)
        
        print("\n=== 测试完成 ===")
        print("所有模拟API接口已测试完毕！")

if __name__ == "__main__":
    tester = MockAPITester()
    tester.run_all_tests()
