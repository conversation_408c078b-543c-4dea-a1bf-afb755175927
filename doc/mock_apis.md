# 模拟API接口说明

本文档说明了当前已实现的模拟API接口，这些接口提供固定内容返回，用于开发阶段测试。将来会替换为真实的API集成和大模型处理。

## 已实现的模拟接口

### 1. 公司信息查询（模拟企查查API）

**接口**: `GET /api/companies/search?name=关键词`

**功能**: 模拟企查查API，根据公司名称关键词返回模拟的公司信息

**实现位置**: `backend/app/company_routes.py` - `search_companies()`

**返回内容**:
- 根据搜索关键词生成3个模拟公司
- 包含公司名称、法定代表人、统一社会信用代码、注册资本等信息
- 同时搜索数据库中已有的公司

**示例返回**:
```json
[
  {
    "name": "测试科技有限公司",
    "legal_representative": "张三",
    "unified_social_credit_code": "91110108MA01234567",
    "registered_capital": "1000万元",
    "address": "北京市海淀区中关村大街1号",
    "business_scope": "技术开发、技术推广、技术转让、技术咨询、技术服务",
    "website": "https://test.com"
  }
]
```

### 2. 微信登录（模拟）

**接口**: 
- `GET /api/auth/login/wechat/qr` - 获取登录二维码
- `POST /api/auth/login/wechat` - 微信登录
- `GET /api/auth/login/check_status?state=状态码` - 检查登录状态

**功能**: 模拟微信登录流程

**实现位置**: `backend/app/auth_routes.py`

**说明**: 
- 生成模拟的二维码URL和状态码
- 根据提供的code创建或查找用户
- 返回JWT token用于后续认证

### 3. 支付宝登录（模拟）

**接口**:
- `GET /api/auth/login/alipay/qr` - 获取登录二维码
- `POST /api/auth/login/alipay` - 支付宝登录
- `GET /api/auth/login/check_status?state=状态码` - 检查登录状态

**功能**: 模拟支付宝登录流程

**实现位置**: `backend/app/auth_routes.py`

**说明**: 类似微信登录，使用auth_code进行用户创建和认证

### 4. 导入招标文件（模拟解析）

**接口**: `POST /api/documents/tender`

**功能**: 上传招标文件并返回模拟的解析结果

**实现位置**: `backend/app/document_routes.py` - `upload_tender_document()`

**模拟内容**:
```json
{
  "project_name": "项目名称项目",
  "tender_unit": "某某政府采购中心",
  "project_budget": "500万元",
  "bid_deadline": "2024-12-31 17:00:00",
  "project_location": "北京市海淀区",
  "project_description": "本项目为政府信息化建设项目，包括软件开发、系统集成等内容。",
  "technical_requirements": "要求具备相关资质，有类似项目经验，技术方案完整可行。",
  "commercial_requirements": "报价合理，付款方式灵活，质保期不少于3年。",
  "qualification_requirements": "具备软件企业认定证书、ISO9001质量管理体系认证等。"
}
```

### 5. 导入参考标书（模拟内容生成）

**接口**: `POST /api/bids/import`

**功能**: 上传标书文件并返回模拟的标书内容

**实现位置**: `backend/app/bid_routes.py` - `import_bid()`

**模拟内容**: 生成包含以下章节的完整标书：
- 项目概述
- 公司简介
- 技术方案（总体架构、实施方案、质量保证）
- 商务报价（报价说明、付款方式、质保服务）
- 项目团队
- 公司资质
- 成功案例
- 服务承诺

### 6. 生成标书（模拟AI生成）

**接口**: `POST /api/bids/generate`

**功能**: 基于招标文件和参考标书生成新的投标文件

**实现位置**: `backend/app/bid_routes.py` - `generate_bid_document()`

**模拟内容**: 生成包含以下内容的详细标书：
- 项目概述和背景
- 公司简介和优势
- 详细技术方案（架构、技术选型、实施方案、质量保证）
- 商务报价和付款方式
- 项目团队介绍
- 公司资质证书
- 成功案例展示
- 风险控制措施
- 服务承诺
- 联系方式

**特点**:
- 内容长度约3000+字符
- 包含公司信息动态填充
- 记录生成时间和参考文件
- 建立标书引用关系

## 测试方法

### 自动化测试

运行测试脚本：
```bash
cd backend
python test_mock_apis.py
```

测试脚本会自动：
1. 注册并登录测试用户
2. 测试公司信息查询
3. 测试微信/支付宝登录
4. 创建测试公司
5. 测试招标文件上传
6. 测试标书导入
7. 测试标书生成

### 手动测试

使用Postman或curl工具测试各个接口，确保：
- 认证机制正常工作
- 返回数据格式正确
- 模拟内容完整合理

## 注意事项

1. **临时实现**: 这些都是临时的模拟实现，用于开发阶段测试
2. **固定内容**: 返回的内容是预设的固定模板，不是真实解析结果
3. **将来替换**: 正式版本中会替换为：
   - 真实的企查查API集成
   - 真实的微信/支付宝登录API
   - 大模型文档解析和内容生成
4. **数据持久化**: 文件上传和标书创建会正常保存到数据库
5. **权限控制**: 保持正常的用户权限和公司关联验证

## 开发计划

后续开发中需要替换的功能：
- [ ] 集成真实企查查API
- [ ] 集成微信开放平台API
- [ ] 集成支付宝开放平台API
- [ ] 实现大模型文档解析
- [ ] 实现AI标书生成
- [ ] 优化生成内容的个性化和准确性
