#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户信息修改功能
"""
import requests
import json

# 后端API基础URL
BASE_URL = "http://localhost:5001/api"

def test_user_update():
    """测试用户信息修改"""

    # 1. 首先尝试登录获取token
    print("1. 尝试登录...")
    login_data = {
        "phone": "13800138000",
        "code": "123456"  # 使用测试验证码
    }

    # 直接使用新的登录接口
    login_response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    print(f"登录响应: {login_response.status_code} - {login_response.text}")

    if login_response.status_code != 200:
        print("登录失败，无法继续测试")
        return

    # 获取token
    login_result = login_response.json()
    if 'token' in login_result:
        token = login_result['token']
    else:
        print("登录响应中没有token")
        return
    
    # 2. 测试获取用户信息
    print("\n2. 获取用户信息...")
    headers = {"Authorization": f"Bearer {token}"}
    profile_response = requests.get(f"{BASE_URL}/user/profile", headers=headers)
    print(f"获取用户信息响应: {profile_response.status_code} - {profile_response.text}")
    
    if profile_response.status_code != 200:
        print("获取用户信息失败")
        return
    
    # 3. 测试更新用户信息
    print("\n3. 更新用户信息...")
    update_data = {
        "username": "测试用户更新",
        "email": "<EMAIL>"
    }
    
    update_response = requests.put(f"{BASE_URL}/user/profile", 
                                 headers=headers, 
                                 json=update_data)
    print(f"更新用户信息响应: {update_response.status_code} - {update_response.text}")
    
    # 4. 测试各种错误情况
    print("\n4. 测试错误情况...")
    
    # 测试空数据
    empty_response = requests.put(f"{BASE_URL}/user/profile", 
                                headers=headers, 
                                json={})
    print(f"空数据响应: {empty_response.status_code} - {empty_response.text}")
    
    # 测试无效数据
    invalid_response = requests.put(f"{BASE_URL}/user/profile", 
                                  headers=headers, 
                                  json=None)
    print(f"无效数据响应: {invalid_response.status_code} - {invalid_response.text}")
    
    # 测试重复用户名
    duplicate_response = requests.put(f"{BASE_URL}/user/profile", 
                                    headers=headers, 
                                    json={"username": "测试用户更新"})
    print(f"重复用户名响应: {duplicate_response.status_code} - {duplicate_response.text}")

if __name__ == "__main__":
    test_user_update()
