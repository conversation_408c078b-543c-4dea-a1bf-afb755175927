import { LitElement, html, css } from 'lit';
import '@material/web/button/filled-button.js';
import '@material/web/button/text-button.js';
import '@material/web/icon/icon.js';

export class WelcomePage extends LitElement {
  static properties = {
    currentCompany: { type: Object },
    hasExistingBids: { type: Boolean },
  };

  static styles = css`
    .container {
      padding: 24px;
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
    }

    .welcome-card {
      background: white;
      border-radius: 12px;
      padding: 48px 32px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }

    .title {
      color: #1976d2;
      margin-bottom: 16px;
      font-size: 2rem;
      font-weight: 500;
    }

    .subtitle {
      color: #666;
      font-size: 1.1rem;
      margin-bottom: 32px;
      line-height: 1.6;
    }

    .description {
      color: #555;
      font-size: 1rem;
      margin-bottom: 24px;
      line-height: 1.5;
      text-align: left;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 32px;
    }

    .secondary-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 24px;
    }

    .company-info {
      background: #f5f5f5;
      border-radius: 8px;
      padding: 16px;
      margin-top: 24px;
      text-align: left;
    }

    .company-name {
      font-weight: 500;
      color: #1976d2;
      margin-bottom: 8px;
    }

    .company-details {
      color: #666;
      font-size: 0.9rem;
      line-height: 1.4;
    }

    @media (max-width: 600px) {
      .container {
        padding: 16px;
      }
      
      .welcome-card {
        padding: 32px 24px;
      }
      
      .title {
        font-size: 1.5rem;
      }
      
      .actions {
        flex-direction: column;
        align-items: center;
      }
      
      .secondary-actions {
        flex-direction: column;
        align-items: center;
      }
    }
  `;

  constructor() {
    super();
    this.currentCompany = null;
    this.hasExistingBids = false;
  }

  render() {
    return html`
      <div class="container">
        <div class="welcome-card">
          <h1 class="title">欢迎使用易中2.0！</h1>
          
          ${this._renderMainContent()}
          
          ${this.currentCompany ? this._renderCompanyInfo() : ''}
        </div>
      </div>
    `;
  }

  _renderMainContent() {
    if (this.hasExistingBids) {
      return html`
        <p class="subtitle">
          您的标书管理平台已准备就绪，可以继续您的工作。
        </p>
        
        <div class="description">
          <p>您可以：</p>
          <ul style="text-align: left; margin: 16px 0;">
            <li>打开已有的标书项目继续编辑</li>
            <li>导入新的招标文件创建标书</li>
            <li>从其他来源导入现有标书</li>
            <li>管理您的公司信息和设置</li>
          </ul>
        </div>

        <div class="actions">
          <md-filled-button @click=${this._onOpenBid}>
            <md-icon slot="icon">folder_open</md-icon>
            打开标书
          </md-filled-button>
          
          <md-filled-button @click=${this._onCreateBid}>
            <md-icon slot="icon">add_circle</md-icon>
            新建标书
          </md-filled-button>
        </div>

        <div class="secondary-actions">
          <md-text-button @click=${this._onImportBid}>
            <md-icon slot="icon">upload</md-icon>
            导入标书
          </md-text-button>
          
          <md-text-button @click=${this._onSwitchCompany}>
            <md-icon slot="icon">business</md-icon>
            切换为其他公司
          </md-text-button>
        </div>
      `;
    } else {
      return html`
        <p class="subtitle">
          您还没有创建任何标书。现在可以开始创建您的第一个标书项目了。
        </p>
        
        <div class="description">
          <p>开始使用易中2.0的简单步骤：</p>
          <ol style="text-align: left; margin: 16px 0;">
            <li>上传招标文件，系统将自动解析关键信息</li>
            <li>选择参考标书模板（可选），提高创建效率</li>
            <li>系统自动生成标书框架，您可以进一步编辑完善</li>
            <li>导出最终的标书文档</li>
          </ol>
        </div>

        <div class="actions">
          <md-filled-button @click=${this._onCreateBid}>
            <md-icon slot="icon">add_circle</md-icon>
            新建标书
          </md-filled-button>
        </div>

        <div class="secondary-actions">
          <md-text-button @click=${this._onImportBid}>
            <md-icon slot="icon">upload</md-icon>
            导入现有标书
          </md-text-button>
          
          <md-text-button @click=${this._onSwitchCompany}>
            <md-icon slot="icon">business</md-icon>
            切换为其他公司
          </md-text-button>
        </div>
      `;
    }
  }

  _renderCompanyInfo() {
    return html`
      <div class="company-info">
        <div class="company-name">当前公司：${this.currentCompany.name}</div>
        <div class="company-details">
          ${this.currentCompany.address ? html`地址：${this.currentCompany.address}<br>` : ''}
          ${this.currentCompany.contact ? html`联系人：${this.currentCompany.contact}<br>` : ''}
          ${this.currentCompany.phone ? html`电话：${this.currentCompany.phone}` : ''}
        </div>
      </div>
    `;
  }

  _onCreateBid() {
    this.dispatchEvent(new CustomEvent('create-bid', {
      bubbles: true,
      composed: true
    }));
  }

  _onOpenBid() {
    this.dispatchEvent(new CustomEvent('open-bid', {
      bubbles: true,
      composed: true
    }));
  }

  _onImportBid() {
    this.dispatchEvent(new CustomEvent('import-bid', {
      bubbles: true,
      composed: true
    }));
  }

  _onSwitchCompany() {
    this.dispatchEvent(new CustomEvent('switch-company', {
      bubbles: true,
      composed: true
    }));
  }
}

customElements.define('welcome-page', WelcomePage);
