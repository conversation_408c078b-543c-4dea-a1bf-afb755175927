import { LitElement, html, css } from 'lit';
import { ReduxElement } from './store/lit-redux.js';
import { api, setCredentials } from './store/index.js';
import '@material/web/textfield/filled-text-field.js';
import '@material/web/button/filled-button.js';
import '@material/web/dialog/dialog.js';
import '@material/web/tabs/primary-tab.js';
import '@material/web/tabs/tabs.js';

class LoginComponent extends ReduxElement {
  static styles = css`
    :host {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      padding: 20px;
    }

    .login-container {
      max-width: 420px;
      width: 100%;
      margin: 0 auto;
      padding: 40px 32px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        0 0 0 1px rgba(255, 255, 255, 0.2);
      display: flex;
      flex-direction: column;
      gap: 24px;
      position: relative;
      z-index: 100; /* 提高z-index，确保在品牌标识之上 */
      animation: slideInUp 0.6s ease-out;
    }

    .login-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      border-radius: 24px;
      z-index: -1;
    }

    .login-title {
      font-size: 1.6rem;
      font-weight: 600;
      color: #1976d2;
      text-align: center;
      margin-bottom: 8px;
    }

    .login-subtitle {
      font-size: 0.95rem;
      color: #666;
      text-align: center;
      margin-bottom: 16px;
    }

    .qr-img {
      width: 200px;
      height: 200px;
      margin: 24px auto 0 auto;
      display: block;
      background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
      border-radius: 16px;
      border: 2px solid rgba(25, 118, 210, 0.1);
      transition: all 0.3s ease;
    }

    .qr-img:hover {
      transform: scale(1.02);
      box-shadow: 0 8px 25px rgba(25, 118, 210, 0.15);
    }

    /* Material Web组件样式调整 */
    md-tabs {
      margin-bottom: 8px;
    }

    md-filled-text-field {
      --md-filled-text-field-container-color: rgba(25, 118, 210, 0.04);
      --md-filled-text-field-focus-outline-color: #1976d2;
    }

    md-filled-button {
      --md-filled-button-container-color: #1976d2;
      --md-filled-button-hover-container-color: #1565c0;
      border-radius: 12px;
      height: 48px;
    }

    /* 错误提示样式 */
    .error-message {
      color: #d32f2f;
      font-size: 0.9rem;
      text-align: center;
      padding: 12px;
      background: rgba(211, 47, 47, 0.1);
      border-radius: 8px;
      border-left: 4px solid #d32f2f;
    }

    /* 成功提示样式 */
    .success-message {
      color: #2e7d32;
      font-size: 0.9rem;
      text-align: center;
      padding: 12px;
      background: rgba(46, 125, 50, 0.1);
      border-radius: 8px;
      border-left: 4px solid #2e7d32;
    }

    /* 加载状态 */
    .loading {
      opacity: 0.7;
      pointer-events: none;
    }

    /* 动画效果 */
    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      :host {
        padding: 16px;
        padding-top: 120px; /* 为品牌标识留出空间 */
      }

      .login-container {
        padding: 32px 24px;
        border-radius: 20px;
      }
    }

    @media (max-width: 480px) {
      :host {
        padding: 16px;
        padding-top: 100px; /* 在小屏幕上调整顶部间距 */
      }

      .login-container {
        padding: 28px 20px; /* 稍微减少内边距以节省空间 */
        border-radius: 20px;
      }

      .login-title {
        font-size: 1.4rem;
      }

      .qr-img {
        width: 180px;
        height: 180px;
      }
    }
  `;

  static properties = {
    phone: { type: String },
    code: { type: String },
    codeSent: { type: Boolean },
    qrType: { type: String },
    qrOpen: { type: Boolean },
    qrUrl: { type: String },
    qrState: { type: String },
    resendTimer: { type: Number },
    activeTab: { type: Number, reflect: true },
    isLoading: { type: Boolean },
    error: { type: String },
  };

  constructor() {
    super();
    this.phone = '';
    this.code = '';
    this.codeSent = false;
    this.qrType = '';
    this.qrOpen = false;
    this.qrUrl = '';
    this.qrState = '';
    this.resendTimer = 0;
    this.resendTimeout = 60;
    this.resendIntervalId = null;
    this.qrCheckIntervalId = null;
    this.activeTab = 0;
    this.isLoading = false;
    this.error = '';
  }

  firstUpdated() {
    // 确保 attribute -> property 同步后再做相关逻辑
    // 可在此处访问 this.activeTab
  }



  onTabChange(e) {
    this.activeTab = e.target.activeTabIndex;

    // 如果切换到微信或支付宝登录，自动生成二维码
    if (this.activeTab === 1) {
      this.generateMockQR('wechat');
    } else if (this.activeTab === 2) {
      this.generateMockQR('alipay');
    }

    this.requestUpdate();
  }

  render() {
    return html`
      <div class="login-container">
        <md-tabs .activeIndex=${this.activeTab} @change=${this.onTabChange.bind(this)}>
          <md-primary-tab>手机号登录</md-primary-tab>
          <md-primary-tab>微信登录</md-primary-tab>
          <md-primary-tab>支付宝登录</md-primary-tab>
        </md-tabs>
        ${this.activeTab === 0
          ? html`
              <div class="login-title">手机号登录</div>
              <div class="login-subtitle">请输入您的手机号码获取验证码</div>
              <md-filled-text-field
                label="手机号"
                type="tel"
                maxlength="11"
                .value=${this.phone}
                @input=${e => (this.phone = e.target.value)}
              ></md-filled-text-field>
              ${this.codeSent
                ? html`
                    <md-filled-text-field
                      label="验证码"
                      maxlength="6"
                      .value=${this.code}
                      @input=${e => (this.code = e.target.value)}
                    ></md-filled-text-field>
                    <md-filled-button @click=${this.onVerifyCode.bind(this)} ?disabled=${this.isLoading}>
                      ${this.isLoading ? '登录中...' : '登录'}
                    </md-filled-button>
                    <md-filled-button @click=${this.onSendCode.bind(this)} .disabled=${this.resendTimer > 0}>
                      ${this.resendTimer > 0 ? `重新发送(${this.resendTimer}s)` : '重新发送'}
                    </md-filled-button>
                  `
                : html`
                    <md-filled-button @click=${this.onSendCode.bind(this)} .disabled=${!/^1\d{10}$/.test(this.phone) || this.isLoading}>
                      ${this.isLoading ? '发送中...' : '发送验证码'}
                    </md-filled-button>
                  `}
              ${this.error ? html`<div class="error-message">${this.error}</div>` : ''}
            `
          : this.activeTab === 1
          ? html`
              <div class="login-title">微信登录</div>
              <div class="login-subtitle">使用微信扫码快速登录</div>
              <div style="text-align: center; padding: 20px;">
                ${this.qrUrl ? html`
                  <img class="qr-img" src=${this.qrUrl} alt="微信登录二维码" />
                ` : html`
                  <div class="qr-img" style="display: flex; align-items: center; justify-content: center; border: 2px dashed #07c160; background: linear-gradient(135deg, #f0f9ff 0%, #e8f5e8 100%);">
                    <div style="text-align: center;">
                      <div style="font-size: 48px; margin-bottom: 10px;">📱</div>
                      <div style="font-size: 14px; color: #07c160; font-weight: 500;">正在生成二维码...</div>
                    </div>
                  </div>
                `}
                <div style="margin-top: 16px; color: #666; font-size: 14px;">
                  请使用微信扫码登录
                </div>
              </div>
              ${this.error ? html`<div class="error-message">${this.error}</div>` : ''}
            `
          : html`
              <div class="login-title">支付宝登录</div>
              <div class="login-subtitle">使用支付宝扫码快速登录</div>
              <div style="text-align: center; padding: 20px;">
                ${this.qrUrl ? html`
                  <img class="qr-img" src=${this.qrUrl} alt="支付宝登录二维码" />
                ` : html`
                  <div class="qr-img" style="display: flex; align-items: center; justify-content: center; border: 2px dashed #1677ff; background: linear-gradient(135deg, #f0f9ff 0%, #e8f4ff 100%);">
                    <div style="text-align: center;">
                      <div style="font-size: 48px; margin-bottom: 10px;">💳</div>
                      <div style="font-size: 14px; color: #1677ff; font-weight: 500;">正在生成二维码...</div>
                    </div>
                  </div>
                `}
                <div style="margin-top: 16px; color: #666; font-size: 14px;">
                  请使用支付宝扫码登录
                </div>
              </div>
              ${this.error ? html`<div class="error-message">${this.error}</div>` : ''}
            `}
      </div>
    `;
  }

  async onSendCode() {
    if (!this.phone || !/^1\d{10}$/.test(this.phone)) {
      this.error = '请输入正确的手机号';
      return;
    }

    this.isLoading = true;
    this.error = '';

    try {
      console.log('[DEBUG] 发送验证码请求:', { phone: this.phone });
      const result = await this.dispatch(api.endpoints.sendSmsCode.initiate({ phone: this.phone })).unwrap();
      console.log('[DEBUG] 发送验证码成功:', result);
      this.codeSent = true;
      this.resendTimer = this.resendTimeout;
      this.startResendTimer();
    } catch (error) {
      console.error('[DEBUG] 发送验证码失败:', error);
      this.error = error.data?.error || error.message || '发送验证码失败';
    } finally {
      this.isLoading = false;
    }
  }

  startResendTimer() {
    if (this.resendIntervalId) clearInterval(this.resendIntervalId);
    this.resendIntervalId = setInterval(() => {
      this.resendTimer--;
      this.requestUpdate();
      if (this.resendTimer <= 0) {
        clearInterval(this.resendIntervalId);
        this.resendIntervalId = null;
      }
    }, 1000);
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    if (this.resendIntervalId) clearInterval(this.resendIntervalId);
    if (this.qrCheckIntervalId) clearInterval(this.qrCheckIntervalId);
  }

  stateChanged(state) {
    // Redux状态变化处理
    // 这里可以根据需要处理认证状态变化
  }

  async onVerifyCode() {
    if (!this.phone || !this.code) {
      this.error = '请输入手机号和验证码';
      return;
    }

    this.isLoading = true;
    this.error = '';

    try {
      const result = await this.dispatch(api.endpoints.login.initiate({
        phone: this.phone,
        code: this.code
      })).unwrap();

      // 设置认证信息
      this.dispatch(setCredentials({
        user: result.user,
        token: result.token
      }));

      this.dispatchEvent(new CustomEvent('login-success', {
        detail: result,
        bubbles: true,
        composed: true
      }));
    } catch (error) {
      this.error = error.data?.error || '登录失败';
    } finally {
      this.isLoading = false;
    }
  }

  generateMockQR(type) {
    // 生成模拟二维码，并开始轮询检查登录状态
    this.qrType = type;
    this.qrOpen = true;
    this.error = '';

    // 生成一个模拟的二维码URL和状态码
    const mockState = `mock_${type}_${Date.now()}`;
    this.qrState = mockState;
    this.qrUrl = `data:image/svg+xml;base64,${btoa(`
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="200" fill="white"/>
        <rect x="20" y="20" width="160" height="160" fill="none" stroke="black" stroke-width="2"/>
        <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="12" fill="black">
          ${type === 'wechat' ? '微信' : '支付宝'}登录二维码
        </text>
        <text x="100" y="120" text-anchor="middle" font-family="Arial" font-size="10" fill="gray">
          模拟二维码
        </text>
      </svg>
    `)}`;

    // 开始轮询检查登录状态
    this.startQrStatusCheck();
  }

  generateMockQR(type) {
    // 生成模拟二维码，并开始轮询检查登录状态
    this.qrType = type;
    this.qrOpen = true;
    this.error = '';

    // 生成一个模拟的二维码URL和状态码
    const mockState = `mock_${type}_${Date.now()}`;
    this.qrState = mockState;

    // 生成模拟二维码图片（SVG格式）
    const qrColor = type === 'wechat' ? '#07c160' : '#1677ff';
    const qrIcon = type === 'wechat' ? '📱' : '💳';
    const qrText = type === 'wechat' ? '微信' : '支付宝';

    this.qrUrl = `data:image/svg+xml;base64,${btoa(`
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="200" fill="white" stroke="${qrColor}" stroke-width="2"/>
        <!-- 模拟二维码方块 -->
        <rect x="20" y="20" width="20" height="20" fill="${qrColor}"/>
        <rect x="50" y="20" width="10" height="10" fill="${qrColor}"/>
        <rect x="70" y="20" width="20" height="20" fill="${qrColor}"/>
        <rect x="100" y="20" width="10" height="10" fill="${qrColor}"/>
        <rect x="120" y="20" width="20" height="20" fill="${qrColor}"/>
        <rect x="150" y="20" width="30" height="30" fill="${qrColor}"/>

        <rect x="20" y="50" width="10" height="10" fill="${qrColor}"/>
        <rect x="40" y="50" width="30" height="10" fill="${qrColor}"/>
        <rect x="80" y="50" width="10" height="10" fill="${qrColor}"/>
        <rect x="100" y="50" width="20" height="20" fill="${qrColor}"/>
        <rect x="130" y="50" width="10" height="10" fill="${qrColor}"/>
        <rect x="150" y="50" width="30" height="10" fill="${qrColor}"/>

        <rect x="20" y="70" width="20" height="20" fill="${qrColor}"/>
        <rect x="50" y="70" width="10" height="10" fill="${qrColor}"/>
        <rect x="70" y="70" width="30" height="10" fill="${qrColor}"/>
        <rect x="110" y="70" width="10" height="10" fill="${qrColor}"/>
        <rect x="130" y="70" width="20" height="20" fill="${qrColor}"/>
        <rect x="160" y="70" width="20" height="20" fill="${qrColor}"/>

        <!-- 中心图标 -->
        <circle cx="100" cy="100" r="25" fill="white" stroke="${qrColor}" stroke-width="2"/>
        <text x="100" y="110" text-anchor="middle" font-family="Arial" font-size="20">${qrIcon}</text>

        <!-- 底部更多方块 -->
        <rect x="20" y="130" width="30" height="10" fill="${qrColor}"/>
        <rect x="60" y="130" width="10" height="10" fill="${qrColor}"/>
        <rect x="80" y="130" width="20" height="20" fill="${qrColor}"/>
        <rect x="110" y="130" width="10" height="10" fill="${qrColor}"/>
        <rect x="130" y="130" width="30" height="10" fill="${qrColor}"/>
        <rect x="170" y="130" width="10" height="10" fill="${qrColor}"/>

        <rect x="20" y="150" width="10" height="10" fill="${qrColor}"/>
        <rect x="40" y="150" width="20" height="20" fill="${qrColor}"/>
        <rect x="70" y="150" width="10" height="10" fill="${qrColor}"/>
        <rect x="90" y="150" width="30" height="10" fill="${qrColor}"/>
        <rect x="130" y="150" width="10" height="10" fill="${qrColor}"/>
        <rect x="150" y="150" width="30" height="30" fill="${qrColor}"/>

        <rect x="20" y="170" width="20" height="10" fill="${qrColor}"/>
        <rect x="50" y="170" width="10" height="10" fill="${qrColor}"/>
        <rect x="70" y="170" width="30" height="10" fill="${qrColor}"/>
        <rect x="110" y="170" width="20" height="10" fill="${qrColor}"/>
        <rect x="140" y="170" width="10" height="10" fill="${qrColor}"/>
      </svg>
    `)}`;

    // 开始轮询检查登录状态（模拟）
    this.startMockQrStatusCheck();
  }

  // 原有的外部API调用方法（注释掉，将来可能恢复）
  async onQrLogin(type) {
    // TODO: 将来恢复外部API调用时使用
    /*
    this.isLoading = true;
    this.error = '';
    this.qrType = type;

    try {
      let result;
      if (type === 'wechat') {
        result = await this.dispatch(api.endpoints.getWechatQR.initiate()).unwrap();
      } else {
        result = await this.dispatch(api.endpoints.getAlipayQR.initiate()).unwrap();
      }

      this.qrUrl = result.qr_url;
      this.qrState = result.state;
      this.qrOpen = true;

      // 开始轮询检查登录状态
      this.startQrStatusCheck();
    } catch (error) {
      this.error = error.data?.error || '获取二维码失败';
    } finally {
      this.isLoading = false;
    }
    */

    // 暂时使用模拟二维码
    this.generateMockQR(type);
  }

  startMockQrStatusCheck() {
    // 模拟二维码状态检查：10秒后自动登录成功
    if (this.qrCheckIntervalId) {
      clearInterval(this.qrCheckIntervalId);
    }

    let countdown = 10; // 10秒倒计时
    console.log(`[模拟] ${this.qrType}二维码生成成功，${countdown}秒后自动登录`);

    this.qrCheckIntervalId = setInterval(() => {
      countdown--;
      console.log(`[模拟] ${this.qrType}登录倒计时: ${countdown}秒`);

      if (countdown <= 0) {
        // 模拟登录成功
        clearInterval(this.qrCheckIntervalId);
        this.qrCheckIntervalId = null;

        // 创建模拟用户数据
        const mockUser = {
          id: Date.now(),
          username: `${this.qrType === 'wechat' ? '微信' : '支付宝'}用户_${Math.random().toString(36).substr(2, 6)}`,
          phone: null,
          email: null,
          wechat_openid: this.qrType === 'wechat' ? `mock_wechat_${Date.now()}` : null,
          alipay_userid: this.qrType === 'alipay' ? `mock_alipay_${Date.now()}` : null
        };

        const mockToken = `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 10)}`;

        console.log(`[模拟] ${this.qrType}登录成功:`, mockUser);

        this.dispatch(setCredentials({
          user: mockUser,
          token: mockToken
        }));

        this.dispatchEvent(new CustomEvent('login-success', {
          detail: { user: mockUser, token: mockToken },
          bubbles: true,
          composed: true
        }));
      }
    }, 1000); // 每1秒检查一次

    // 5分钟后停止轮询（防止意外情况）
    setTimeout(() => {
      if (this.qrCheckIntervalId) {
        clearInterval(this.qrCheckIntervalId);
        this.qrCheckIntervalId = null;
        this.error = '二维码已过期，请刷新页面重试';
        this.qrOpen = false;
      }
    }, 300000);
  }

  // 原有的外部API状态检查方法（注释掉，将来可能恢复）
  startQrStatusCheck() {
    // TODO: 将来恢复外部API调用时使用
    /*
    if (this.qrCheckIntervalId) {
      clearInterval(this.qrCheckIntervalId);
    }

    this.qrCheckIntervalId = setInterval(async () => {
      try {
        const result = await this.dispatch(api.endpoints.checkLoginStatus.initiate(this.qrState)).unwrap();

        if (result.status === 'success') {
          // 登录成功
          clearInterval(this.qrCheckIntervalId);
          this.qrCheckIntervalId = null;

          this.dispatch(setCredentials({
            user: result.user,
            token: result.token
          }));

          this.dispatchEvent(new CustomEvent('login-success', {
            detail: result,
            bubbles: true,
            composed: true
          }));
        } else if (result.status === 'failed') {
          // 登录失败
          clearInterval(this.qrCheckIntervalId);
          this.qrCheckIntervalId = null;
          this.error = result.error || '登录失败';
          this.qrOpen = false;
        }
        // pending状态继续轮询
      } catch (error) {
        // 网络错误等，继续轮询
        console.warn('检查登录状态失败:', error);
      }
    }, 2000); // 每2秒检查一次

    // 10分钟后停止轮询
    setTimeout(() => {
      if (this.qrCheckIntervalId) {
        clearInterval(this.qrCheckIntervalId);
        this.qrCheckIntervalId = null;
        this.error = '二维码已过期，请重新获取';
        this.qrOpen = false;
      }
    }, 600000);
    */
  }
}

customElements.define('login-component', LoginComponent);
