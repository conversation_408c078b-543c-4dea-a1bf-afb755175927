import { LitElement, html, css } from 'lit';
import '@material/web/button/filled-button.js';
import '@material/web/button/outlined-button.js';
import '@material/web/icon/icon.js';
import '@material/web/iconbutton/icon-button.js';
import '@material/web/progress/linear-progress.js';
import '@material/web/textfield/filled-text-field.js';

export class BidImport extends LitElement {
  static properties = {
    loading: { type: Boolean },
    error: { type: String },
    success: { type: String },
    uploadProgress: { type: Number },
    uploading: { type: Boolean },
    selectedFile: { type: Object },
    parsing: { type: <PERSON><PERSON><PERSON> },
    parseProgress: { type: Number },
    parsedContent: { type: Object },
    bidTitle: { type: String },
    bidDescription: { type: String },
  };

  static styles = css`
    :host {
      display: block;
      max-width: 800px;
      margin: 0 auto;
    }

    .container {
      background: white;
      border-radius: 12px;
      padding: 32px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }

    .title {
      font-size: 24px;
      font-weight: 600;
      color: #1976d2;
      margin-bottom: 24px;
      text-align: center;
    }

    .upload-area {
      border: 2px dashed #e0e0e0;
      border-radius: 12px;
      padding: 48px 24px;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease;
      margin-bottom: 24px;
    }

    .upload-area:hover {
      border-color: #1976d2;
      background: #f8f9ff;
    }

    .upload-area.dragover {
      border-color: #1976d2;
      background: #f0f4ff;
    }

    .upload-icon {
      font-size: 48px;
      color: #1976d2;
      margin-bottom: 16px;
    }

    .upload-text {
      font-size: 18px;
      color: #333;
      margin-bottom: 8px;
    }

    .upload-hint {
      font-size: 14px;
      color: #666;
    }

    .file-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      margin-bottom: 16px;
      background: #f8f9fa;
    }

    .file-item md-icon {
      margin-right: 12px;
      color: #1976d2;
    }

    .file-info {
      flex: 1;
    }

    .file-name {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .file-meta {
      font-size: 14px;
      color: #666;
    }

    .progress-container {
      margin: 16px 0;
    }

    .progress-text {
      text-align: center;
      margin-top: 8px;
      color: #666;
      font-size: 14px;
    }

    .content-preview {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      margin: 16px 0;
      border-left: 4px solid #1976d2;
    }

    .content-preview h3 {
      margin: 0 0 12px 0;
      color: #1976d2;
      font-size: 16px;
    }

    .content-text {
      max-height: 200px;
      overflow-y: auto;
      font-size: 14px;
      line-height: 1.5;
      color: #333;
      white-space: pre-wrap;
    }

    .form-section {
      margin: 24px 0;
    }

    .form-section h3 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 18px;
    }

    .form-row {
      margin-bottom: 16px;
    }

    .actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-top: 32px;
    }

    .error {
      background: #ffebee;
      color: #c62828;
      padding: 12px 16px;
      border-radius: 8px;
      margin: 16px 0;
      border-left: 4px solid #c62828;
    }

    .success {
      background: #e8f5e8;
      color: #2e7d32;
      padding: 12px 16px;
      border-radius: 8px;
      margin: 16px 0;
      border-left: 4px solid #2e7d32;
    }

    .hidden {
      display: none;
    }

    .step-indicator {
      display: flex;
      justify-content: center;
      margin-bottom: 32px;
    }

    .step {
      display: flex;
      align-items: center;
      color: #666;
    }

    .step.active {
      color: #1976d2;
      font-weight: 500;
    }

    .step.completed {
      color: #4caf50;
    }

    .step-number {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: #e0e0e0;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      margin-right: 8px;
    }

    .step.active .step-number {
      background: #1976d2;
    }

    .step.completed .step-number {
      background: #4caf50;
    }

    .step-separator {
      width: 40px;
      height: 2px;
      background: #e0e0e0;
      margin: 0 16px;
    }

    .step.completed + .step-separator {
      background: #4caf50;
    }
  `;

  constructor() {
    super();
    this.loading = false;
    this.error = '';
    this.success = '';
    this.uploadProgress = 0;
    this.uploading = false;
    this.selectedFile = null;
    this.parsing = false;
    this.parseProgress = 0;
    this.parsedContent = null;
    this.bidTitle = '';
    this.bidDescription = '';
  }

  render() {
    return html`
      <div class="container">
        <div class="title">导入标书</div>

        <!-- 步骤指示器 -->
        <div class="step-indicator">
          <div class="step ${this.selectedFile ? 'completed' : 'active'}">
            <span class="step-number">1</span>
            选择文件
          </div>
          <div class="step-separator"></div>
          <div class="step ${this.parsedContent ? 'completed' : (this.selectedFile ? 'active' : '')}">
            <span class="step-number">2</span>
            解析内容
          </div>
          <div class="step-separator"></div>
          <div class="step ${this.parsedContent ? 'active' : ''}">
            <span class="step-number">3</span>
            确认导入
          </div>
        </div>

        ${this._renderCurrentStep()}

        <!-- 错误和成功消息 -->
        ${this.error ? html`<div class="error">${this.error}</div>` : ''}
        ${this.success ? html`<div class="success">${this.success}</div>` : ''}
      </div>
    `;
  }

  _renderCurrentStep() {
    if (!this.selectedFile) {
      return this._renderFileSelection();
    } else if (!this.parsedContent) {
      return this._renderParsing();
    } else {
      return this._renderConfirmation();
    }
  }

  _renderFileSelection() {
    return html`
      <div class="upload-area" 
           @click=${this._onUploadClick} 
           @dragover=${this._onDragOver} 
           @dragleave=${this._onDragLeave}
           @drop=${this._onDrop}>
        <md-icon class="upload-icon">cloud_upload</md-icon>
        <div class="upload-text">点击或拖拽文件到此处上传</div>
        <div class="upload-hint">支持 PDF、DOC、DOCX 格式，最大 50MB</div>
      </div>
      <input type="file" class="hidden" accept=".pdf,.doc,.docx" @change=${this._onFileChange}>

      ${this.uploading ? html`
        <div class="progress-container">
          <md-linear-progress .value=${this.uploadProgress / 100}></md-linear-progress>
          <div class="progress-text">上传中... ${this.uploadProgress}%</div>
        </div>
      ` : ''}
    `;
  }

  _renderParsing() {
    return html`
      <div class="file-item">
        <md-icon>description</md-icon>
        <div class="file-info">
          <div class="file-name">${this.selectedFile.name}</div>
          <div class="file-meta">${this._formatFileSize(this.selectedFile.size)}</div>
        </div>
        <md-icon-button @click=${this._removeFile} title="重新选择文件">
          <md-icon>close</md-icon>
        </md-icon-button>
      </div>

      ${this.parsing ? html`
        <div class="progress-container">
          <md-linear-progress .value=${this.parseProgress / 100}></md-linear-progress>
          <div class="progress-text">正在解析文件内容... ${this.parseProgress}%</div>
        </div>
      ` : html`
        <div class="actions">
          <md-outlined-button @click=${this._removeFile}>重新选择</md-outlined-button>
          <md-filled-button @click=${this._parseFile}>开始解析</md-filled-button>
        </div>
      `}
    `;
  }

  _renderConfirmation() {
    return html`
      <div class="file-item">
        <md-icon>description</md-icon>
        <div class="file-info">
          <div class="file-name">${this.selectedFile.name}</div>
          <div class="file-meta">${this._formatFileSize(this.selectedFile.size)} • 解析完成</div>
        </div>
      </div>

      ${this.parsedContent ? html`
        <div class="content-preview">
          <h3>解析的内容预览</h3>
          <div class="content-text">${this.parsedContent.preview || '无法预览内容'}</div>
        </div>

        <div class="form-section">
          <h3>标书信息</h3>
          <div class="form-row">
            <md-filled-text-field
              label="标书标题"
              .value=${this.bidTitle}
              @input=${(e) => this.bidTitle = e.target.value}
              style="width: 100%;"
            ></md-filled-text-field>
          </div>
          <div class="form-row">
            <md-filled-text-field
              label="标书描述"
              type="textarea"
              rows="3"
              .value=${this.bidDescription}
              @input=${(e) => this.bidDescription = e.target.value}
              style="width: 100%;"
            ></md-filled-text-field>
          </div>
        </div>
      ` : ''}

      <div class="actions">
        <md-outlined-button @click=${this._removeFile}>重新选择文件</md-outlined-button>
        <md-filled-button @click=${this._importBid} ?disabled=${!this.bidTitle.trim() || this.loading}>
          ${this.loading ? '导入中...' : '确认导入'}
        </md-filled-button>
      </div>
    `;
  }

  // 事件处理方法
  _onUploadClick() {
    const input = this.shadowRoot.querySelector('input[type="file"]');
    input.click();
  }

  _onFileChange(e) {
    const file = e.target.files[0];
    if (file) {
      this._handleFile(file);
    }
  }

  _onDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
  }

  _onDragLeave(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
  }

  _onDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      this._handleFile(files[0]);
    }
  }

  _handleFile(file) {
    // 验证文件类型 - 使用更宽松的检测逻辑
    if (!this._isValidFileType(file)) {
      this.error = '请选择 PDF、DOC 或 DOCX 格式的文件';
      return;
    }

    // 验证文件大小 (50MB)
    if (file.size > 50 * 1024 * 1024) {
      this.error = '文件大小不能超过50MB';
      return;
    }

    this.error = '';
    this._uploadFile(file);
  }

  _isValidFileType(file) {
    // 获取文件扩展名
    const fileName = file.name.toLowerCase();
    const extension = fileName.split('.').pop();

    // 允许的扩展名
    const allowedExtensions = ['pdf', 'doc', 'docx'];

    // 首先检查扩展名
    if (!allowedExtensions.includes(extension)) {
      return false;
    }

    // 然后检查MIME类型（更宽松的检测）
    const allowedMimeTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      // 添加一些常见的变体
      'application/doc',
      'application/ms-word',
      'application/word',
      'application/vnd.ms-word',
      'application/vnd.ms-word.document.macroEnabled.12',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
      // 某些系统可能返回空的MIME类型
      '',
      'application/octet-stream'
    ];

    // 如果MIME类型匹配，直接通过
    if (file.type && allowedMimeTypes.includes(file.type)) {
      return true;
    }

    // 如果MIME类型不匹配但扩展名正确，给出警告但仍然允许
    if (allowedExtensions.includes(extension)) {
      console.warn(`文件 ${file.name} 的MIME类型 (${file.type}) 可能不正确，但扩展名正确，允许上传`);
      return true;
    }

    return false;
  }

  async _uploadFile(file) {
    this.uploading = true;
    this.uploadProgress = 0;

    try {
      // 模拟上传进度
      const interval = setInterval(() => {
        this.uploadProgress += 10;
        if (this.uploadProgress >= 100) {
          clearInterval(interval);
          this.uploading = false;
          this.selectedFile = file;
          // 自动生成标书标题
          this.bidTitle = file.name.replace(/\.(pdf|doc|docx)$/i, '') + '-导入标书';
        }
      }, 200);

    } catch (error) {
      this.uploading = false;
      this.error = '文件上传失败，请重试';
    }
  }

  async _parseFile() {
    this.parsing = true;
    this.parseProgress = 0;
    this.error = '';

    try {
      // 模拟解析进度
      const interval = setInterval(() => {
        this.parseProgress += 20;
        if (this.parseProgress >= 100) {
          clearInterval(interval);
          this.parsing = false;

          // 模拟解析结果
          this.parsedContent = {
            preview: `这是从文件 "${this.selectedFile.name}" 中解析出的内容预览。\n\n实际实现中，这里会显示从PDF/DOC/DOCX文件中提取的文本内容。\n\n解析出的内容将被转换为标书的节点结构，包括文本段落、表格、图片等元素。`,
            nodes: [
              {
                type: 'text',
                content: '标书正文内容...',
                metadata: {}
              }
            ],
            metadata: {
              pageCount: 10,
              wordCount: 5000,
              hasImages: true,
              hasTables: true
            }
          };
        }
      }, 500);

    } catch (error) {
      this.parsing = false;
      this.error = '文件解析失败，请检查文件格式是否正确';
    }
  }

  async _importBid() {
    if (!this.bidTitle.trim()) {
      this.error = '请输入标书标题';
      return;
    }

    this.loading = true;
    this.error = '';

    try {
      // 触发导入事件
      this.dispatchEvent(new CustomEvent('bid-import', {
        detail: {
          file: this.selectedFile,
          title: this.bidTitle.trim(),
          description: this.bidDescription.trim(),
          parsedContent: this.parsedContent
        },
        bubbles: true,
        composed: true
      }));

      this.success = '标书导入成功！';

      // 重置表单
      setTimeout(() => {
        this._resetForm();
      }, 2000);

    } catch (error) {
      this.error = '导入失败，请重试';
    } finally {
      this.loading = false;
    }
  }

  _removeFile() {
    this.selectedFile = null;
    this.parsedContent = null;
    this.bidTitle = '';
    this.bidDescription = '';
    this.error = '';
    this.success = '';
  }

  _resetForm() {
    this.selectedFile = null;
    this.parsedContent = null;
    this.bidTitle = '';
    this.bidDescription = '';
    this.error = '';
    this.success = '';
    this.uploadProgress = 0;
    this.parseProgress = 0;
  }

  _formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

customElements.define('bid-import', BidImport);
