import { html, css } from 'lit';
import { ReduxElement } from './store/lit-redux.js';
import {
  selectCurrentUser,
  selectIsAuthenticated,
  selectCurrentView,
  selectCurrentCompany,
  selectCurrentCompanyId,
  selectCurrentBid,
  selectBidsList,
  selectBidsLoaded,
  selectNotifications,
  setCurrentView,
  setCurrentCompany,
  setCurrentBid,
  setBidsList,
  addNotification,
  removeNotification,
  api
} from './store/index.js';
import './login-component.js';
import './user-shell.js';
import './user-profile.js';
import './bid-create.js';
import './bid-import.js';
import './bid-open.js';
import './bid-export.js';
import './back-wrapper.js';
import './notification-toast.js';
import './company-add.js';
import './company-edit.js';
import './company-manage.js';
import './multi-step-wizard.js';
import './bid-node-list.js';
import './welcome-page.js';

class EasywinApp extends ReduxElement {
  static styles = css`
    :host {
      display: block;
      min-height: 100vh;
      background: transparent; /* 让背景由body控制 */
    }
  `;

  static properties = {
    user: { type: Object },
    isAuthenticated: { type: Boolean },
    currentView: { type: String },
    currentCompany: { type: Object },
    currentBid: { type: Object },
    bidsList: { type: Array },
    bidsLoaded: { type: Boolean },
    notifications: { type: Array },
    userCompanies: { type: Array },
    currentCompanyId: { type: Number },
    _selectedNode: { type: Object },
  };

  //
  // --- Lifecycle Methods ---
  //

  constructor() {
    super();
    this.user = null;
    this.isAuthenticated = false;
    this.currentView = 'default';
    this.currentCompany = null;
    this.currentBid = null;
    this.bidsList = [];
    this.bidsLoaded = false;
    this.notifications = [];
    this.userCompanies = [];
    this._selectedNode = null;
  }

  connectedCallback() {
    super.connectedCallback();

    // 如果有token，尝试获取用户信息
    const token = localStorage.getItem('token');
    if (token) {
      this.dispatch(api.endpoints.getCurrentUser.initiate())
        .then(async () => {
          // 用户信息获取成功后，恢复当前公司信息
          await this._restoreCurrentCompany();
          // 加载用户公司列表（检查是否首次登录）
          await this._loadUserCompanies();
          // 获取标书列表
          await this._loadBidsList();
          // 标书列表加载完成后，设置默认视图
          this._setInitialView();
        });
    }
  }

  stateChanged(state) {
    this.user = selectCurrentUser(state);
    this.isAuthenticated = selectIsAuthenticated(state);
    this.currentView = selectCurrentView(state);
    this.currentCompany = selectCurrentCompany(state);
    this.currentCompanyId = selectCurrentCompanyId(state);
    this.currentBid = selectCurrentBid(state);
    this.bidsList = selectBidsList(state);
    this.bidsLoaded = selectBidsLoaded(state);
    this.notifications = selectNotifications(state);
  }

  //
  // --- Render Methods ---
  //

  render() {
    if (!this.isAuthenticated) {
      return this._renderLogin();
    }

    if (this.user && (!this.userCompanies || this.userCompanies.length === 0)) {
      return this._renderFirstLoginWizard();
    }

    return html`
      ${this._renderUserShell()}
      ${this._renderNotifications()}
    `;
  }

  _renderLogin() {
    return html`<login-component @login-success=${this.onLoginSuccess.bind(this)}></login-component>`;
  }

  _renderFirstLoginWizard() {
    const steps = [
      {
        title: '完善用户信息',
        description: '请完善您的个人信息，这将帮助我们为您提供更好的服务。',
        component: this._createProfileStep()
      },
      {
        title: '新建公司信息',
        description: '请创建或选择您的公司信息，这将作为您的默认工作环境。',
        component: this._createCompanyStep()
      }
    ];

    return html`
      <multi-step-wizard
        title="欢迎使用易中2.0"
        subtitle="请完成以下设置以开始使用"
        .steps=${steps}
        .onComplete=${() => this._onFirstLoginWizardComplete()}
      ></multi-step-wizard>
    `;
  }

  _renderNotifications() {
    return html`
      <notification-toast
        .notifications=${this.notifications}
        .onRemove=${(id) => this.dispatch(removeNotification(id))}
      ></notification-toast>
    `;
  }

  _renderUserShell() {
    return html`
      <user-shell
        .user=${this.user}
        .currentCompany=${this.currentCompany}
        .currentBid=${this.currentBid}
        .bidsList=${this.bidsList}
        .bidsLoaded=${this.bidsLoaded}
        @logout=${this.onLogout}
        @profile=${() => this.dispatch(setCurrentView('profile'))}
        @navigate=${this.onNavigate}>
        ${this._renderMainContent()}
      </user-shell>
    `;
  }

  _renderMainContent() {
    switch (this.currentView) {
      case 'profile':
        return this._renderProfile();
      case 'company-manage':
        return this._renderCompanyManage();
      case 'company-add':
        return this._renderCompanyAdd();
      case 'company-edit':
        return this._renderCompanyEdit();
      case 'bid-create':
        return this._renderBidCreate();
      case 'bid-open':
        return this._renderBidOpen();
      case 'bid-import':
        return this._renderBidImport();
      case 'bid-export':
        return this._renderBidExport();
      default:
        if (this.currentBid && this.currentBid.root_node) {
          return this._renderBidEdit();
        }
        return this._renderWelcome();
    }
  }

  _renderProfile() {
    return html`
      <back-wrapper @back=${() => this.dispatch(setCurrentView('default'))}>
        <user-profile .user=${this.user} @profile-updated=${this.onProfileUpdated} @submit=${() => this.dispatch(setCurrentView('default'))}></user-profile>
      </back-wrapper>
    `;
  }

  _renderWelcome() {
    return html`
      <welcome-page
        .currentCompany=${this.currentCompany}
        .hasExistingBids=${this.bidsList && this.bidsList.length > 0}
        @create-bid=${this.onWelcomeCreateBid}
        @open-bid=${this.onWelcomeOpenBid}
        @import-bid=${this.onWelcomeImportBid}
        @switch-company=${this.onWelcomeSwitchCompany}
      ></welcome-page>
    `;
  }

  _renderCompanyManage() {
    return html`
      <back-wrapper @back=${this.onBack}>
        <company-manage
          .companies=${this.userCompanies}
          .currentCompany=${this.currentCompany}
          @add-company=${this.onAddCompanyFromManage}
          @switch-company=${this.onSwitchCompanyFromManage}
          @edit-company=${this.onEditCompanyFromManage}
        ></company-manage>
      </back-wrapper>
    `;
  }

  _renderCompanyAdd() {
    return html`
      <back-wrapper @back=${() => this.dispatch(setCurrentView('company-manage'))}>
        <company-add
          .onSearch=${this.onCompanySearch}
          .onCreate=${this.onCompanyCreated}
        ></company-add>
      </back-wrapper>
    `;
  }

  _renderCompanyEdit() {
    return html`
      <back-wrapper @back=${() => this.dispatch(setCurrentView('company-manage'))}>
        <company-edit
          .company=${this.currentCompany}
          .onUpdate=${this.onUpdateCompany}
        ></company-edit>
      </back-wrapper>
    `;
  }

  _renderBidCreate() {
    return html`
      <back-wrapper @back=${this.onBack}>
        <bid-create @bid-created=${this.onBidCreated}></bid-create>
      </back-wrapper>
    `;
  }

  _renderBidOpen() {
    return html`
      <back-wrapper @back=${this.onBack}>
        <bid-open
          .bids=${this.bidsList}
          .loading=${!this.bidsLoaded}
          .currentCompany=${this.currentCompany}
          .currentBid=${this.currentBid}
          @open-bid=${this.onOpenBid}
          @retry-load=${this.onRetryLoadBids}
          @create-new-bid=${this.onCreateNewBid}
          @select-company=${this.onSelectCompany}
          @filter-change=${this.onBidFilterChange}
        ></bid-open>
      </back-wrapper>
    `;
  }

  _renderBidEdit() {
    return html`
        <div style="display: flex; height: calc(100vh - 120px);">
          <div style="width: 300px; overflow-y: auto; border-right: 1px solid #e0e0e0; padding: 16px;">
            <bid-node-list
              .nodes=${[this.currentBid.root_node]}
              @node-selected=${this._onNodeSelected}>
            </bid-node-list>
          </div>
          <div style="flex: 1; padding: 16px; overflow-y: auto;">
            ${this._selectedNode
              ? html`<node-editor .node=${this._selectedNode}></node-editor>`
              : html`<div style="text-align: center; color: #757575; margin-top: 40px;">请从左侧选择一个章节进行编辑</div>`
            }
          </div>
        </div>
      `;
  }

  _renderBidImport() {
    return html`
      <back-wrapper @back=${this.onBack}>
        <bid-import @bid-import=${this.onBidImport}></bid-import>
      </back-wrapper>
    `;
  }

  _renderBidExport() {
    return html`
      <back-wrapper @back=${this.onBack}>
        <bid-export
          .currentBid=${this.currentBid}
          @export-bid=${this.onExportBid}
        ></bid-export>
      </back-wrapper>
    `;
  }

  //
  // --- Event Handlers ---
  //

  // Auth
  async onLoginSuccess() {
    // 登录成功后，Redux会自动更新用户状态
    this.dispatch(addNotification({
      type: 'success',
      message: '登录成功！'
    }));

    // 检查是否是首次登录（公司列表为空）
    await this._loadUserCompanies();

    // 获取标书列表
    await this._loadBidsList();

    // 设置初始视图
    this._setInitialView();

    // 触发认证状态变化事件
    window.dispatchEvent(new CustomEvent('auth-state-changed'));
  }

  onLogout() {
    // 通过API进行登出
    this.dispatch(api.endpoints.logout.initiate())
      .then(() => {
        this.dispatch(addNotification({
          type: 'success',
          message: '已安全退出'
        }));

        // 触发认证状态变化事件
        window.dispatchEvent(new CustomEvent('auth-state-changed'));
      });
  }

  // First Login Wizard
  _onFirstLoginWizardComplete() {
    this.dispatch(addNotification({
      type: 'success',
      message: '欢迎使用易中2.0！'
    }));
    this._loadUserCompanies();
    this.requestUpdate();
  }

  // Profile
  onProfileUpdated(e) {
    // 通过API更新用户资料
    this.dispatch(api.endpoints.updateProfile.initiate(e.detail))
      .then(() => {
        this.dispatch(setCurrentView('default'));
        this.dispatch(addNotification({
          type: 'success',
          message: '个人信息更新成功'
        }));
      })
      .catch(() => {
        this.dispatch(addNotification({
          type: 'error',
          message: '更新失败，请重试'
        }));
      });
  }

  // Company
  onCompanySearch = async (keyword) => {
    try {
      const result = await this.dispatch(api.endpoints.searchCompanies.initiate(keyword));
      const companies = result.data || [];

      // 映射后端字段名到前端期望的字段名
      return companies.map(company => ({
        ...company,
        legal: company.legal_representative, // 映射法人字段
        url: company.website // 映射网址字段
      }));
    } catch (error) {
      this.dispatch(addNotification({
        type: 'error',
        message: '搜索公司失败'
      }));
      return [];
    }
  };

  onCompanyCreated = async (companyData) => {
    try {
      const result = await this.dispatch(api.endpoints.createCompany.initiate({
        name: companyData.name,
        legal_representative: companyData.legal,
        website: companyData.url
      }));

      if (result.data) {
        this.dispatch(setCurrentCompany(result.data.company));
        this.dispatch(addNotification({
          type: 'success',
          message: '公司创建成功！'
        }));
        // 刷新用户公司列表并回到默认视图
        await this._loadUserCompanies();
        this.dispatch(setCurrentView('default'));
      }
    } catch (error) {
      this.dispatch(addNotification({
        type: 'error',
        message: '创建公司失败'
      }));
      throw error;
    }
  };

  onUpdateCompany = async (companyId, updateData) => {
    try {
      const result = await this.dispatch(api.endpoints.updateCompany.initiate({
        id: companyId,
        ...updateData
      }));

      if (result.data) {
        // 如果更新的是当前公司，同步更新当前公司状态
        if (this.currentCompany && this.currentCompany.id === companyId) {
          this.dispatch(setCurrentCompany({
            ...this.currentCompany,
            ...updateData
          }));
        }

        this.dispatch(addNotification({
          type: 'success',
          message: '公司信息更新成功！'
        }));
        // 刷新公司管理页面并回到默认视图
        await this._loadUserCompanies();
        this.dispatch(setCurrentView('default'));
      }
    } catch (error) {
      this.dispatch(addNotification({
        type: 'error',
        message: '更新公司信息失败'
      }));
      throw error;
    }
  };

  onSwitchCompany = async (company) => {
    try {
      // 切换公司时，清空当前标书
      this.dispatch(setCurrentBid(null));
      
      // 直接更新Redux中的当前公司（存储在localStorage中）
      this.dispatch(setCurrentCompany(company));
      this.dispatch(addNotification({
        type: 'success',
        message: `已切换到 ${company.name}`
      }));

      // 重新加载标书列表并回到默认视图
      await this._loadBidsList();
      this.dispatch(setCurrentView('default'));
    } catch (error) {
      this.dispatch(addNotification({
        type: 'error',
        message: '切换公司失败'
      }));
      throw error;
    }
  };

  onAddCompanyFromManage = () => {
    this.dispatch(setCurrentView('company-add'));
  };

  onEditCompanyFromManage = (e) => {
    const { company } = e.detail;
    this.dispatch(setCurrentCompany(company));
    this.dispatch(setCurrentView('company-edit'));
  };

  onSwitchCompanyFromManage = (e) => {
    const { company } = e.detail;
    this.onSwitchCompany(company);
  };

  // Bid Create
  onBidCreated = (e) => {
    console.log('标书创建成功:', e.detail);
    this.dispatch(addNotification({
      type: 'success',
      message: '标书创建成功！'
    }));
    // 设置为当前标书并跳转到默认视图（编辑模式）
    this.dispatch(setCurrentBid(e.detail.bid));
    this.dispatch(setCurrentView('default'));
    // 重新加载标书列表
    this._loadBidsList();
  };

  // Bid Open
  onOpenBid = (e) => {
    const { bidId, bid } = e.detail;
    console.log('打开标书:', bidId, bid);

    // 清空上次选择的节点
    this._selectedNode = null;

    // 设置为当前标书并跳转到默认视图（编辑模式）
    this.dispatch(setCurrentBid(bid));
    this.dispatch(addNotification({
      type: 'success',
      message: `已打开标书: ${bid.title}`
    }));
    this.dispatch(setCurrentView('default'));
  };

  onRetryLoadBids = () => {
    this._loadBidsList();
  };

  onCreateNewBid = () => {
    this.dispatch(setCurrentView('bid-create'));
  };

  onSelectCompany = () => {
    this.dispatch(setCurrentView('company-manage'));
  };

  onBidFilterChange = async (e) => {
    const { search, status } = e.detail;

    try {
      // 构建查询参数
      const params = {};
      if (this.currentCompanyId) {
        params.company_id = this.currentCompanyId;
      }
      if (search) {
        params.search = search;
      }
      if (status) {
        params.status = status;
      }

      // 调用API获取过滤后的标书列表
      const result = await this.dispatch(api.endpoints.getBids.initiate(params));
      if (result.data) {
        this.dispatch(setBidsList(result.data.bids || []));
      }
    } catch (error) {
      console.warn('Failed to filter bids:', error);
      this.dispatch(addNotification({
        type: 'error',
        message: '筛选标书失败'
      }));
    }
  };

  // Bid Edit
  _onNodeSelected = (e) => {
    const { nodeId } = e.detail;
    if (!this.currentBid || !this.currentBid.root_node) {
      this._selectedNode = null;
      return;
    }

    const findNodeRecursive = (node, id) => {
      if (node.id === id) {
        return node;
      }
      if (node.children) {
        for (const child of node.children) {
          const found = findNodeRecursive(child, id);
          if (found) {
            return found;
          }
        }
      }
      return null;
    };

    this._selectedNode = findNodeRecursive(this.currentBid.root_node, nodeId);
    this.requestUpdate(); // 确保Lit重新渲染
  }

  // Bid Import
  onBidImport = async (e) => {
    console.log('开始导入标书:', e.detail);

    const { file, title, description } = e.detail;

    try {
      // 检查是否有当前公司
      if (!this.currentCompany) {
        console.error('没有选择当前公司');
        this.dispatch(addNotification({
          type: 'error',
          message: '请先选择一个公司'
        }));
        return;
      }

      // 创建FormData
      const formData = new FormData();
      formData.append('file', file);
      formData.append('title', title);
      formData.append('description', description);
      formData.append('company_id', this.currentCompany.id);

      console.log('发送导入请求，公司ID:', this.currentCompany.id);

      // 调用导入API
      const result = await this.dispatch(api.endpoints.importBid.initiate(formData));

      console.log('API响应结果:', result);

      if (result.data) {
        console.log('标书导入成功:', result.data);
        this.dispatch(addNotification({
          type: 'success',
          message: '标书导入成功！'
        }));

        // 设置为当前标书并跳转到默认视图（编辑模式）
        this.dispatch(setCurrentBid(result.data.bid));
        this.dispatch(setCurrentView('default'));
      } else if (result.error) {
        console.error('标书导入失败:', result.error);
        let errorMessage = '标书导入失败，请重试';

        if (result.error.status === 500) {
          errorMessage = '服务器内部错误，请检查文件格式或联系管理员';
        } else if (result.error.data?.error) {
          errorMessage = result.error.data.error;
        }

        this.dispatch(addNotification({
          type: 'error',
          message: errorMessage
        }));
      }

    } catch (error) {
      console.error('标书导入异常:', error);
      this.dispatch(addNotification({
        type: 'error',
        message: '标书导入失败，请重试'
      }));
    }
  };

  // Bid Export
  onExportBid = async (e) => {
    const { bidId, format, onProgress, onComplete, onError } = e.detail;

    try {
      // 模拟进度更新
      let progress = 0;
      const progressInterval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress >= 100) {
          progress = 100;
          clearInterval(progressInterval);
        }
        onProgress(Math.floor(progress));
      }, 500);

      // 等待进度完成
      await new Promise(resolve => {
        const checkProgress = () => {
          if (progress >= 100) {
            resolve();
          } else {
            setTimeout(checkProgress, 100);
          }
        };
        checkProgress();
      });

      // 调用导出API
      const result = await this.dispatch(api.endpoints.exportBid.initiate({
        id: bidId,
        format: format
      }));

      if (result.data) {
        // 创建下载链接
        const blob = result.data;
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;

        // 使用标书名称作为文件名
        const bidTitle = this.currentBid?.title || '标书';
        const safeTitle = bidTitle.replace(/[^\w\s-]/g, '').trim();
        const extension = format === 'pdf' ? '.pdf' : '.docx';
        a.download = `${safeTitle}${extension}`;

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        onComplete();

        this.dispatch(addNotification({
          type: 'success',
          message: `标书导出成功: ${safeTitle}${extension}`
        }));
      } else {
        throw new Error('导出失败');
      }
    } catch (error) {
      console.error('Export error:', error);
      onError('导出失败，请重试');
      this.dispatch(addNotification({
        type: 'error',
        message: '导出标书失败'
      }));
    }
  };

  // Welcome Page
  onWelcomeCreateBid = () => {
    this.dispatch(setCurrentView('bid-create'));
  };

  onWelcomeOpenBid = () => {
    this.dispatch(setCurrentView('bid-open'));
  };

  onWelcomeImportBid = () => {
    this.dispatch(setCurrentView('bid-import'));
  };

  onWelcomeSwitchCompany = () => {
    this.dispatch(setCurrentView('company-manage'));
  };

  // Generic Navigation
  onBack = () => {
    // 返回时，清除当前标书并回到默认视图
    this.dispatch(setCurrentBid(null));
    this.dispatch(setCurrentView('default'));
  };

  onNavigate = (e) => {
    this.dispatch(setCurrentView(e.detail.view));
  };

  //
  // --- Internal Logic ---
  //

  // Initialization
  _setInitialView() {
    this.dispatch(setCurrentView('default'));
  }

  async _restoreCurrentCompany() {
    if (this.currentCompanyId) {
      try {
        const result = await this.dispatch(api.endpoints.getCompanyById.initiate(this.currentCompanyId));
        if (result.data) {
          // 只更新 currentCompany，不更新 currentCompanyId（避免触发 localStorage 写入）
          this.dispatch({
            type: 'app/setCurrentCompany',
            payload: result.data,
            meta: { skipIdUpdate: true }
          });
        } else {
          // 如果公司不存在了，清除存储的ID
          this.dispatch(setCurrentCompany(null));
        }
      } catch (error) {
        console.warn('恢复当前公司信息失败:', error);
        // 清除无效的公司ID
        this.dispatch(setCurrentCompany(null));
      }
    }
  }

  // Data Loading
  async _loadUserCompanies() {
    try {
      const result = await this.dispatch(api.endpoints.getUserCompanies.initiate());
      this.userCompanies = result.data || [];
      this.requestUpdate(); // 触发重新渲染
    } catch (error) {
      console.warn('获取用户公司列表失败:', error);
      this.dispatch(addNotification({
        type: 'error',
        message: '获取公司列表失败'
      }));
    }
  }

  async _loadBidsList() {
    try {
      // 构建查询参数
      const params = {};
      if (this.currentCompanyId) {
        params.company_id = this.currentCompanyId;
      }

      const result = await this.dispatch(api.endpoints.getBids.initiate(params));
      if (result.data) {
        this.dispatch(setBidsList(result.data.bids || []));
      }
    } catch (error) {
      console.warn('Failed to load bids list:', error);
      this.dispatch(setBidsList([]));
    }
  }

  // First Login Wizard Helpers
  _createProfileStep() {
    const userProfile = document.createElement('user-profile');
    userProfile.user = this.user;
    userProfile.addEventListener('profile-updated', (e) => {
      this.onProfileUpdated(e);
      const wizard = this.shadowRoot.querySelector('multi-step-wizard');
      if (wizard) {
        wizard.markStepCompleted(0);
      }
    });
    return userProfile;
  }

  _createCompanyStep() {
    const companyAdd = document.createElement('company-add');
    companyAdd.onSearch = this.onCompanySearch;
    companyAdd.onCreate = async (companyData) => {
      await this.onCompanyCreated(companyData);
      const wizard = this.shadowRoot.querySelector('multi-step-wizard');
      if (wizard) {
        wizard.markStepCompleted(1);
      }
    };
    return companyAdd;
  }
}

customElements.define('easywin-app', EasywinApp);
