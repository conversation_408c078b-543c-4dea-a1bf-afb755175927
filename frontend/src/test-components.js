import { LitElement, html, css } from 'lit';
import '@material/web/select/filled-select.js';
import '@material/web/select/select-option.js';
import '@material/web/list/list.js';
import '@material/web/list/list-item.js';
import './login-component.js';
import './user-shell.js';
import './user-profile.js';
import './company-add.js';
import './company-edit.js';
import './company-manage.js';
import './bid-create.js';
import './bid-open.js';
import './bid-import.js';
import './bid-export.js';
import './back-wrapper.js';
import './multi-step-wizard.js';
import './base-editor.js';
import './text-editor.js';
import './image-editor.js';
import './table-editor.js';
import './bid-node-list.js';
import './node-editor.js';
import './welcome-page.js';

const COMPONENTS = [
  { value: 'login', label: 'login-component', events: ['login-success'] },
  { value: 'user-shell', label: 'user-shell', events: ['logout', 'profile', 'navigate'] },
  { value: 'user-profile', label: 'user-profile', events: ['profile-updated'] },
  { value: 'company-add', label: 'company-add', events: ['company-added', 'company-search'] },
  { value: 'company-edit', label: 'company-edit', events: ['company-updated', 'edit-cancelled'] },
  { value: 'company-manage', label: 'company-manage', events: ['add-company', 'switch-company', 'edit-company'] },
  { value: 'bid-create', label: 'bid-create', events: ['bid-created'] },
  { value: 'bid-open', label: 'bid-open', events: ['open-bid', 'retry-load', 'create-new-bid', 'select-company', 'filter-change'] },
  { value: 'bid-import', label: 'bid-import', events: ['bid-import'] },
  { value: 'bid-export', label: 'bid-export', events: ['export-bid'] },
  { value: 'back-wrapper', label: 'back-wrapper', events: ['back'] },
  { value: 'multi-step-wizard', label: 'multi-step-wizard', events: [] },
  { value: 'text-editor', label: 'text-editor', events: ['edit-start', 'edit-save', 'edit-cancel', 'edit-error', 'node-delete'] },
  { value: 'image-editor', label: 'image-editor', events: ['edit-start', 'edit-save', 'edit-cancel', 'edit-error', 'node-delete', 'image-upload'] },
  { value: 'table-editor', label: 'table-editor', events: ['edit-start', 'edit-save', 'edit-cancel', 'edit-error', 'node-delete'] },
  { value: 'bid-node-list', label: 'bid-node-list', events: ['node-selected'] },
  { value: 'node-editor', label: 'node-editor', events: ['node-save'] },
  { value: 'welcome', label: 'welcome-page', events: ['create-bid', 'open-bid', 'import-bid', 'switch-company'] },
];

const mockUser = {
  username: '测试用户',
  phone: '***********',
  email: '<EMAIL>',
  wechat_openid: 'mock_wechat',
  company: { name: '测试公司' }
};

const mockCompanies = [
  {
    id: 1,
    name: '北京字节跳动科技有限公司',
    legal_representative: '张一鸣',
    unified_social_credit_code: '91110108MA01234567',
    registered_capital: '1000万元',
    contact_phone: '010-12345678',
    contact_email: '<EMAIL>',
    website: 'https://bytedance.com',
    address: '北京市海淀区知春路63号',
    business_scope: '技术开发、技术推广、技术转让、技术咨询、技术服务',
    description: '字节跳动是一家全球化的互联网技术公司',
    is_default: false
  },
  {
    id: 2,
    name: '阿里巴巴（中国）有限公司',
    legal_representative: '马云',
    unified_social_credit_code: '91330100MA2876543210',
    registered_capital: '5000万元',
    contact_phone: '0571-87654321',
    contact_email: '<EMAIL>',
    website: 'https://alibaba.com',
    address: '浙江省杭州市余杭区文一西路969号',
    business_scope: '电子商务、云计算、数字娱乐、金融科技',
    description: '阿里巴巴集团是以电子商务为核心的数字经济体',
    is_default: true
  },
  {
    id: 3,
    name: '腾讯科技（深圳）有限公司',
    legal_representative: '马化腾',
    unified_social_credit_code: '91440300708461136T',
    registered_capital: '2000万元',
    contact_phone: '0755-86013388',
    contact_email: '<EMAIL>',
    website: 'https://tencent.com',
    address: '深圳市南山区科技中一路腾讯大厦',
    business_scope: '互联网信息服务、游戏开发、社交网络',
    description: '腾讯是一家世界领先的互联网科技公司',
    is_default: false
  }
];

const mockCurrentCompany = mockCompanies[1]; // 阿里巴巴作为当前公司

const mockBids = [
  {
    id: 1,
    title: '阿里云数据中心建设项目投标书',
    description: '为阿里云在华东地区建设新数据中心的投标文件',
    status: 'draft',
    created_at: '2023-11-01T09:00:00',
    updated_at: '2023-11-15T16:30:00',
    tender_deadline: '2023-12-31T23:59:59',
    project_budget: '5000万元',
    project_location: '浙江省杭州市',
    contact_person: '张工程师',
    contact_phone: '0571-88888888',
    company_id: 2
  },
  {
    id: 2,
    title: '电商平台技术升级项目',
    description: '淘宝平台核心系统技术升级改造项目',
    status: 'in_progress',
    created_at: '2023-10-15T14:20:00',
    updated_at: '2023-11-20T10:15:00',
    tender_deadline: '2024-01-15T18:00:00',
    project_budget: '8000万元',
    project_location: '浙江省杭州市',
    contact_person: '李技术总监',
    contact_phone: '0571-99999999',
    company_id: 2
  },
  {
    id: 3,
    title: '智慧物流仓储系统建设',
    description: '菜鸟网络智慧物流仓储管理系统开发项目',
    status: 'completed',
    created_at: '2023-09-01T08:30:00',
    updated_at: '2023-10-30T17:45:00',
    tender_deadline: '2023-11-30T23:59:59',
    project_budget: '3000万元',
    project_location: '江苏省南京市',
    contact_person: '王项目经理',
    contact_phone: '025-66666666',
    company_id: 2
  },
  {
    id: 4,
    title: '移动支付安全系统升级',
    description: '支付宝移动支付安全防护系统全面升级项目',
    status: 'submitted',
    created_at: '2023-08-10T11:00:00',
    updated_at: '2023-09-25T14:20:00',
    tender_deadline: '2023-10-31T23:59:59',
    project_budget: '6000万元',
    project_location: '浙江省杭州市',
    contact_person: '赵安全专家',
    contact_phone: '0571-77777777',
    company_id: 2
  },
  {
    id: 5,
    title: '企业级云服务平台开发',
    description: '面向中小企业的一站式云服务平台建设项目',
    status: 'draft',
    created_at: '2023-11-10T13:45:00',
    updated_at: '2023-11-18T09:30:00',
    tender_deadline: '2024-02-28T23:59:59',
    project_budget: '4500万元',
    project_location: '上海市浦东新区',
    contact_person: '孙产品经理',
    contact_phone: '021-55555555',
    company_id: 2
  }
];

// mock 公司数据，用于演示搜索功能
const mockCompanyData = [
  { name: '北京字节跳动科技有限公司', legal: '张一鸣', url: 'https://bytedance.com' },
  { name: '阿里巴巴（中国）有限公司', legal: '马云', url: 'https://alibaba.com' },
  { name: '腾讯科技（深圳）有限公司', legal: '马化腾', url: 'https://tencent.com' },
  { name: '华为技术有限公司', legal: '任正非', url: 'https://huawei.com' },
  { name: '百度在线网络技术（北京）有限公司', legal: '李彦宏', url: 'https://baidu.com' },
  { name: '小米科技有限责任公司', legal: '雷军', url: 'https://mi.com' },
  { name: '京东集团', legal: '刘强东', url: 'https://jd.com' },
  { name: '美团', legal: '王兴', url: 'https://meituan.com' },
  { name: '滴滴出行', legal: '程维', url: 'https://didiglobal.com' },
  { name: '拼多多', legal: '黄峥', url: 'https://pdd.com' },
  { name: '网易', legal: '丁磊', url: 'https://netease.com' },
  { name: '新浪', legal: '曹国伟', url: 'https://sina.com' },
  { name: '搜狐', legal: '张朝阳', url: 'https://sohu.com' },
  { name: '360', legal: '周鸿祎', url: 'https://360.cn' },
  { name: '快手', legal: '宿华', url: 'https://kuaishou.com' },
];

class TestComponents extends LitElement {
  static styles = css`
    .container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #f5f5f5;
    }
    .picker {
      position: fixed;
      right: 24px;
      bottom: 24px;
      z-index: 1000;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.10);
      padding: 12px 20px;
      display: flex;
      align-items: center;
      gap: 12px;
    }
    .demo-card {
      min-width: 340px;
      max-width: 900px;
      margin: 0 auto;
      padding: 32px 24px;
      box-sizing: border-box;
    }
    .event-log {
      margin-top: 24px;
      width: 100%;
      max-width: 600px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 1px 4px rgba(0,0,0,0.06);
      padding: 16px 20px;
      font-size: 0.98rem;
      color: #333;
      min-height: 40px;
    }
    .event-log h3 {
      font-size: 1.05rem;
      font-weight: 500;
      margin: 0 0 12px 0;
      color: #1976d2;
    }
    .log-entries {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .log-entry {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 4px;
      font-family: monospace;
      font-size: 0.9rem;
    }
    .timestamp {
      color: #666;
      white-space: nowrap;
    }
    .event-name {
      color: #1976d2;
      font-weight: 500;
      white-space: nowrap;
    }
    .event-detail {
      color: #333;
      word-break: break-all;
      flex: 1;
    }
  `;

  static properties = {
    selected: { type: String },
    eventLog: { type: Array },
  };

  constructor() {
    super();
    const saved = localStorage.getItem('test-component-selected');
    this.selected = saved || COMPONENTS[0].value;
    this.eventLog = [];
  }

  render() {
    return html`
      <div class="container">
        <div class="demo-card">
          ${this._renderDemo()}
        </div>
        ${this.eventLog.length > 0 ? html`
          <div class="event-log">
            <h3>事件日志</h3>
            <div class="log-entries">
              ${this.eventLog.map(entry => html`
                <div class="log-entry">
                  <span class="timestamp">[${entry.timestamp}]</span>
                  <span class="event-name">${entry.event}</span>
                  <span class="event-detail">${JSON.stringify(entry.detail)}</span>
                </div>
              `)}
            </div>
          </div>
        ` : ''}
      </div>
      <div class="picker">
        <label for="component-select">组件选择：</label>
        <md-filled-select id="component-select" @input=${this._onSelect} .value=${this.selected}>
          ${COMPONENTS.map(c => html`<md-select-option .value=${c.value}>${c.label}</md-select-option>`)}
        </md-filled-select>
      </div>
    `;
  }

  firstUpdated() {
    this._setupEventListeners();
  }

  updated(changed) {
    if (changed.has('selected')) {
      this.eventLog = [];
      this._setupEventListeners();
    }
  }

  _renderDemo() {
    if (this.selected === 'login') {
      return html`<login-component id="demo-el"></login-component>`;
    } else if (this.selected === 'user-shell') {
      return html`<user-shell id="demo-el" .user=${mockUser}><div style="padding:32px;">user-shell slot 示例内容</div></user-shell>`;
    } else if (this.selected === 'user-profile') {
      return html`<user-profile id="demo-el" .user=${mockUser}></user-profile>`;
    } else if (this.selected === 'company-add') {
      return html`<company-add id="demo-el"></company-add>`;
    } else if (this.selected === 'company-edit') {
      return html`
        <company-edit
          id="demo-el"
          .company=${mockCompanies[0]}
          .onUpdate=${(id, data) => this._handleCompanyUpdate(id, data)}
          .onCancel=${() => this._handleEditCancel()}
        ></company-edit>
      `;
    } else if (this.selected === 'company-manage') {
      return html`
        <company-manage
          id="demo-el"
          .companies=${mockCompanies}
          .currentCompany=${mockCurrentCompany}
          @add-company=${this._handleAddCompanyEvent}
          @switch-company=${this._handleSwitchCompanyEvent}
          @edit-company=${this._handleEditCompanyEvent}
        ></company-manage>
      `;
    } else if (this.selected === 'bid-create') {
      return html`<bid-create id="demo-el"></bid-create>`;
    } else if (this.selected === 'bid-import') {
      return html`<bid-import id="demo-el" @bid-import=${this._handleBidImport}></bid-import>`;
    } else if (this.selected === 'bid-open') {
      return html`
        <bid-open
          id="demo-el"
          .bids=${mockBids}
          .loading=${false}
          .currentCompany=${mockCurrentCompany}
          .currentBid=${mockBids[1]}
        ></bid-open>
      `;
    } else if (this.selected === 'bid-export') {
      return html`
        <bid-export
          id="demo-el"
          .currentBid=${mockBids[0]}
        ></bid-export>
      `;
    } else if (this.selected === 'back-wrapper') {
      return html`
        <back-wrapper id="demo-el">
          <div style="padding: 24px;">
            <h2>这是包装在back-wrapper中的内容</h2>
            <p>点击左上角的"返回"按钮可以触发back事件。</p>
            <div style="background: #f5f5f5; padding: 16px; border-radius: 8px; margin: 16px 0;">
              <h3>功能特性：</h3>
              <ul>
                <li>简单的浮动返回按钮</li>
                <li>不干扰原有布局</li>
                <li>触发back事件</li>
                <li>半透明背景，不太醒目</li>
              </ul>
            </div>
            <p>这个组件可以包装任何需要返回功能的内容，返回按钮会浮动在左上角。</p>
          </div>
        </back-wrapper>
      `;
    } else if (this.selected === 'multi-step-wizard') {
      return html`
        <multi-step-wizard
          id="demo-el"
          .title=${'演示向导'}
          .subtitle=${'这是一个多步骤向导的演示'}
          .steps=${[
            { title: '用户信息', description: '完善您的个人信息' },
            { title: '公司信息', description: '添加公司信息' }
          ]}
          .currentStep=${0}
          .onComplete=${() => alert('向导完成！')}
          .onCancel=${() => alert('向导取消')}
        ></multi-step-wizard>
      `;
    } else if (this.selected === 'text-editor') {
      return html`
        <div style="max-width: 800px; margin: 0 auto; padding: 20px;">
          <h2>文字编辑组件演示</h2>
          <p>这个组件支持富文本编辑，包括字体样式、大小、颜色等。</p>

          <text-editor
            id="demo-el"
            .nodeId=${'text-demo-1'}
            .content=${{
              text: '这是一段示例文字，专注于内容编辑。\n\n样式将在导出时统一应用。'
            }}
            .onSave=${(content, metadata) => {
              console.log('保存文字内容:', content, metadata);
              alert('文字内容已保存！');
            }}
            .onCancel=${() => console.log('取消编辑')}
            .onDelete=${() => {
              if (confirm('确定要删除这个文字块吗？')) {
                alert('文字块已删除');
              }
            }}
          ></text-editor>

          <div style="margin-top: 40px;">
            <h3>另一个文字块</h3>
            <text-editor
              .nodeId=${'text-demo-2'}
              .content=${{
                text: '这是另一个文字块，同样专注于内容。'
              }}
            ></text-editor>
          </div>
        </div>
      `;
    } else if (this.selected === 'image-editor') {
      return html`
        <div style="max-width: 800px; margin: 0 auto; padding: 20px;">
          <h2>图片编辑组件演示</h2>
          <p>这个组件支持图片显示、文字说明编辑和图片替换功能。</p>

          <image-editor
            id="demo-el"
            .nodeId=${'image-demo-1'}
            .content=${{
              imageUrl: 'https://via.placeholder.com/400x300/1976d2/ffffff?text=示例图片',
              caption: '这是一张示例图片，展示图片编辑组件的功能'
            }}
            .onSave=${(content, metadata) => {
              console.log('保存图片内容:', content, metadata);
              alert('图片内容已保存！');
            }}
            .onCancel=${() => console.log('取消编辑')}
            .onDelete=${() => {
              if (confirm('确定要删除这张图片吗？')) {
                alert('图片已删除');
              }
            }}
          ></image-editor>

          <div style="margin-top: 40px;">
            <h3>第二个图片块</h3>
            <p>演示另一张图片的编辑：</p>
            <image-editor
              .nodeId=${'image-demo-2'}
              .content=${{
                imageUrl: 'https://via.placeholder.com/300x200/4caf50/ffffff?text=测试图片2',
                caption: '这是第二张测试图片'
              }}
            ></image-editor>
          </div>
        </div>
      `;
    } else if (this.selected === 'table-editor') {
      return html`
        <div style="max-width: 1000px; margin: 0 auto; padding: 20px;">
          <h2>表格编辑组件演示</h2>
          <p>这个组件支持表格显示、行列操作（增删改、调整顺序）和内容编辑。</p>

          <table-editor
            id="demo-el"
            .nodeId=${'table-demo-1'}
            .content=${{
              title: '产品库存表',
              headers: ['产品名称', '价格', '库存', '状态'],
              data: [
                ['iPhone 15', '¥5999', '100', '有货'],
                ['MacBook Pro', '¥12999', '50', '有货'],
                ['iPad Air', '¥3999', '200', '有货'],
                ['Apple Watch', '¥2499', '150', '有货']
              ]
            }}
            .onSave=${(content, metadata) => {
              console.log('保存表格内容:', content, metadata);
              alert('表格内容已保存！');
            }}
            .onCancel=${() => console.log('取消编辑')}
            .onDelete=${() => {
              if (confirm('确定要删除这个表格吗？')) {
                alert('表格已删除');
              }
            }}
          ></table-editor>

          <div style="margin-top: 40px;">
            <h3>空白表格</h3>
            <p>演示从零开始创建表格：</p>
            <table-editor
              .nodeId=${'table-demo-2'}
              .content=${{}}
            ></table-editor>
          </div>
        </div>
      `;
    } else if (this.selected === 'node-editor') {
      return html`
        <div style="max-width: 1000px; margin: 0 auto; padding: 20px;">
          <h2>节点编辑组件演示</h2>
          <p>这个组件是编辑器容器，可以包含多个不同类型的内容（文本、图片、表格），只有一个编辑器处于编辑状态。</p>

          <div style="margin-bottom: 40px;">
            <h3>包含多种内容的节点</h3>
            <node-editor
              id="demo-el"
              .nodeId=${'node-demo-1'}
              .nodeData=${{
                title: '产品介绍节点',
                content: [
                  {
                    id: 'content-1',
                    type: 'text',
                    content: {
                      text: '我们的产品是一款革命性的智能设备，具有以下特点：\n\n• 高性能处理器\n• 超长续航\n• 智能AI助手\n• 简洁优雅的设计'
                    }
                  },
                  {
                    id: 'content-2',
                    type: 'image',
                    content: {
                      imageUrl: 'https://via.placeholder.com/600x400/1976d2/ffffff?text=产品展示图',
                      caption: '产品外观展示图'
                    }
                  },
                  {
                    id: 'content-3',
                    type: 'table',
                    content: {
                      title: '产品规格参数',
                      headers: ['参数', '规格', '说明'],
                      data: [
                        ['处理器', 'A17 Pro', '最新一代芯片'],
                        ['内存', '8GB', 'LPDDR5'],
                        ['存储', '256GB', 'NVMe SSD'],
                        ['电池', '5000mAh', '支持快充']
                      ]
                    }
                  }
                ],
                metadata: {
                  created_at: '2024-01-15',
                  author: '产品团队'
                }
              }}
              .onSave=${(content, metadata) => {
                console.log('保存节点内容:', content, metadata);
                alert('节点内容已保存！');
              }}
              .onCancel=${() => console.log('取消节点编辑')}
              .onDelete=${() => {
                if (confirm('确定要删除这个节点吗？')) {
                  alert('节点已删除');
                }
              }}
            ></node-editor>
          </div>

          <div style="margin-bottom: 40px;">
            <h3>空白节点</h3>
            <p>演示从空白开始创建节点内容：</p>
            <node-editor
              .nodeId=${'node-demo-2'}
              .nodeData=${{
                title: '新建节点',
                content: [],
                metadata: {}
              }}
            ></node-editor>
          </div>

          <div>
            <h3>只读模式</h3>
            <p>演示只读模式下的节点显示：</p>
            <node-editor
              .nodeId=${'node-demo-3'}
              .readonly=${true}
              .nodeData=${{
                title: '只读节点',
                content: [
                  {
                    id: 'readonly-content-1',
                    type: 'text',
                    content: {
                      text: '这是一个只读节点，无法编辑内容。'
                    }
                  }
                ],
                metadata: {}
              }}
            ></node-editor>
          </div>
        </div>
      `;
    } else if (this.selected === 'bid-node-list') {
      return html`
        <div style="max-width: 800px; margin: 0 auto; padding: 20px;">
          <h2>标书节点列表组件演示</h2>
          <p>这个组件显示标书的节点列表，用户可以点击选择节点。</p>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
              <h3>有节点的列表</h3>
              <bid-node-list
                id="demo-el"
                .nodes=${[
                  { id: 'node-1', name: '项目概述', type: 'text' },
                  { id: 'node-2', name: '技术方案', type: 'text' },
                  { id: 'node-3', name: '系统架构图', type: 'image' },
                  { id: 'node-4', name: '成本预算表', type: 'table' },
                  { id: 'node-5', name: '附件文档', type: 'container' },
                  { id: 'node-6', name: '未分类节点' }
                ]}
                .selectedNodeId=${'node-2'}
              ></bid-node-list>
            </div>

            <div>
              <h3>空节点列表</h3>
              <bid-node-list
                .nodes=${[]}
              ></bid-node-list>
            </div>
          </div>

          <div style="margin-top: 20px; padding: 16px; background: #f5f5f5; border-radius: 8px;">
            <h4>事件监听</h4>
            <p>点击节点会触发 'node-selected' 事件，事件详情会显示在控制台中。</p>
          </div>
        </div>
      `;
    } else if (this.selected === 'welcome') {
      return html`
        <div style="margin: 0 auto;">
          <h2 style="text-align: center; margin-bottom: 20px;">欢迎页面组件演示</h2>

          <div style="margin-bottom: 40px;">
            <h3>无现有标书的情况</h3>
            <welcome-page
              id="demo-el"
              .currentCompany=${mockCompanies[0]}
              .hasExistingBids=${false}
            ></welcome-page>
          </div>

          <div style="margin-bottom: 40px;">
            <h3>有现有标书的情况</h3>
            <welcome-page
              .currentCompany=${mockCompanies[1]}
              .hasExistingBids=${true}
            ></welcome-page>
          </div>

          <div style="margin-bottom: 40px;">
            <h3>无公司信息的情况</h3>
            <welcome-page
              .currentCompany=${null}
              .hasExistingBids=${false}
            ></welcome-page>
          </div>

          <div style="padding: 16px; background: #f5f5f5; border-radius: 8px;">
            <h4>事件说明</h4>
            <p>该组件会触发以下事件：</p>
            <ul>
              <li><code>create-bid</code> - 点击新建标书按钮</li>
              <li><code>open-bid</code> - 点击打开标书按钮</li>
              <li><code>import-bid</code> - 点击导入标书按钮</li>
              <li><code>switch-company</code> - 点击切换为其他公司按钮</li>
            </ul>
          </div>
        </div>
      `;
    }
    return '';
  }

  _onSelect(e) {
    this.selected = e.target.value;
    localStorage.setItem('test-component-selected', this.selected);
  }

  _setupEventListeners() {
    setTimeout(() => {
      const comp = COMPONENTS.find(c => c.value === this.selected);
      if (comp && comp.events) {
        // 对于welcome组件，监听所有的welcome-page元素
        if (this.selected === 'welcome') {
          const welcomeElements = this.renderRoot.querySelectorAll('welcome-page');
          welcomeElements.forEach(el => {
            comp.events.forEach(evt => {
              el.addEventListener(evt, ev => {
                this._logEvent(evt, ev.detail);
              });
            });
          });
          return;
        }

        // 对于其他组件，只监听#demo-el
        const el = this.renderRoot.querySelector('#demo-el');
        if (!el) return;
        comp.events.forEach(evt => {
          el.addEventListener(evt, ev => {
            if (evt === 'company-search') {
              this._handleCompanySearch(ev, el);
            } else if (evt === 'add-company') {
              this._handleAddCompanyEvent(ev);
            } else if (evt === 'switch-company') {
              this._handleSwitchCompanyEvent(ev);
            } else if (evt === 'edit-company') {
              this._handleEditCompanyEvent(ev);
            } else if (evt === 'bid-import') {
              this._handleBidImport(ev);
            } else if (evt === 'export-bid') {
              this._handleExportBid(ev);
            } else if (evt === 'image-upload') {
              this._handleImageUpload(ev);
            } else if (evt === 'edit-start') {
              this._logEvent(evt, ev.detail);
            } else if (evt === 'edit-save') {
              this._logEvent(evt, ev.detail);
              alert('内容已保存！');
            } else if (evt === 'edit-cancel') {
              this._logEvent(evt, ev.detail);
            } else if (evt === 'edit-error') {
              this._logEvent(evt, ev.detail);
              alert(`编辑错误: ${ev.detail.error}`);
            } else if (evt === 'node-delete') {
              this._logEvent(evt, ev.detail);
              if (confirm('确定要删除这个内容块吗？')) {
                alert('内容块已删除');
              }
            } else {
              this._logEvent(evt, ev.detail);
            }
          });
        });
      }
    }, 0);
  }

  _logEvent(eventName, detail) {
    const timestamp = new Date().toLocaleTimeString();
    const entry = {
      timestamp,
      event: eventName,
      detail: detail || {}
    };

    this.eventLog = [...this.eventLog, entry];
    console.log(`[${timestamp}] ${eventName}:`, detail);

    // 对于welcome组件的事件，显示友好的提示
    if (['create-bid', 'open-bid', 'import-bid', 'switch-company'].includes(eventName)) {
      const messages = {
        'create-bid': '新建标书',
        'open-bid': '打开标书',
        'import-bid': '导入标书',
        'switch-company': '切换为其他公司'
      };
      alert(`触发事件: ${messages[eventName]}`);
    }
  }

  _handleCompanySearch(keyword, companyAddElement) {
    console.log('搜索公司:', keyword);

    // 模拟后端搜索延迟
    setTimeout(() => {
      const results = mockCompanyData.filter(c =>
        c.name.includes(keyword)
      ).slice(0, 10); // 增加到10个结果以便测试滚动

      // 调用组件的方法设置搜索结果
      companyAddElement.setSearchResults(results);

      // 也显示一个提示
      console.log(`找到 ${results.length} 个匹配的公司`);
    }, 500);
  }

  // 新增公司管理相关的处理函数
  async _handleGetUserCompanies() {
    console.log('获取用户公司列表');
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockCompanies;
  }

  async _handleSwitchCompany(company) {
    console.log('切换到公司:', company.name);
    alert(`已切换到公司: ${company.name}`);
    // 模拟切换延迟
    await new Promise(resolve => setTimeout(resolve, 300));
  }

  async _handleCompanyUpdate(id, data) {
    console.log('更新公司信息:', id, data);
    alert(`公司信息已更新: ${data.name || '未知公司'}`);
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  _handleEditCancel() {
    console.log('取消编辑公司信息');
    alert('已取消编辑');
  }

  async _handleCompanyCreate(data) {
    console.log('创建公司:', data);
    alert(`公司创建成功: ${data.name}`);
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  async _handleCompanySearchForManage(keyword) {
    console.log('公司管理中搜索公司:', keyword);
    // 模拟搜索延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockCompanyData.filter(c => c.name.includes(keyword)).slice(0, 5);
  }



  // 新的事件处理方法
  _handleAddCompanyEvent(event) {
    console.log('触发新增公司事件');
    this._logEvent('add-company', event.detail);
    alert('触发新增公司事件 - 应该由 easywin-app 调用 company-add 组件');
  }

  _handleSwitchCompanyEvent(event) {
    const company = event.detail.company;
    console.log('触发切换公司事件:', company);
    this._logEvent('switch-company', event.detail);

    // 模拟切换公司：更新 mockCurrentCompany
    const oldCurrentCompany = mockCurrentCompany;

    // 将原来的当前公司设为非默认
    if (oldCurrentCompany) {
      const oldCompanyIndex = mockCompanies.findIndex(c => c.id === oldCurrentCompany.id);
      if (oldCompanyIndex !== -1) {
        mockCompanies[oldCompanyIndex] = { ...mockCompanies[oldCompanyIndex], is_default: false };
      }
    }

    // 将新公司设为当前公司
    const newCompanyIndex = mockCompanies.findIndex(c => c.id === company.id);
    if (newCompanyIndex !== -1) {
      mockCompanies[newCompanyIndex] = { ...mockCompanies[newCompanyIndex], is_default: true };
      // 更新全局当前公司
      Object.assign(mockCurrentCompany, mockCompanies[newCompanyIndex]);
    }

    // 重新渲染组件以显示变化
    this.requestUpdate();

    alert(`已切换到公司: ${company.name} - 界面已更新显示当前公司`);
  }

  _handleEditCompanyEvent(event) {
    const company = event.detail.company;
    console.log('触发编辑公司事件:', company);
    this._logEvent('edit-company', event.detail);
    alert(`触发编辑公司事件: ${company.name} - 应该由 easywin-app 调用 company-edit 组件`);
  }

  async _handleBidImport(event) {
    const { file, title, description, parsedContent } = event.detail;
    console.log('标书导入事件:', {
      fileName: file.name,
      fileSize: file.size,
      title,
      description,
      parsedContent: parsedContent ? '已解析' : '未解析'
    });

    this._logEvent('bid-import', event.detail);

    // 检查是否要进行真实API测试
    const testRealAPI = confirm(`收到标书导入事件！
文件: ${file.name}
标题: ${title}
描述: ${description || '无描述'}
文件大小: ${this._formatFileSize(file.size)}

是否要测试真实的后端API？
点击"确定"进行真实API测试，点击"取消"仅显示模拟结果。`);

    if (testRealAPI) {
      await this._testRealBidImportAPI(event.detail);
    } else {
      // 仅显示模拟结果
      alert(`模拟导入成功！实际应用中，这里会调用后端API保存标书数据。`);
    }
  }

  async _testRealBidImportAPI(eventDetail) {
    const { file, title, description } = eventDetail;

    try {
      // 1. 先尝试登录获取token
      console.log('正在登录获取token...');
      const loginResponse = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ phone: '***********', code: '123456' })
      });

      if (!loginResponse.ok) {
        throw new Error('登录失败');
      }

      const loginResult = await loginResponse.json();
      const token = loginResult.token;
      console.log('登录成功，获取到token');

      // 2. 获取用户公司
      console.log('正在获取用户公司...');
      const companiesResponse = await fetch('/api/companies/user', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!companiesResponse.ok) {
        throw new Error('获取公司列表失败');
      }

      const companies = await companiesResponse.json();
      if (!companies || companies.length === 0) {
        throw new Error('用户没有关联的公司，请先创建公司');
      }

      const company = companies[0];
      console.log('使用公司:', company.name);

      // 3. 调用导入API
      console.log('正在调用导入API...');
      const formData = new FormData();
      formData.append('file', file);
      formData.append('title', title);
      formData.append('description', description);
      formData.append('company_id', company.id);

      const importResponse = await fetch('/api/bids/import', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` },
        body: formData
      });

      const importResult = await importResponse.json();

      if (importResponse.ok) {
        alert(`✅ 真实API测试成功！
标书ID: ${importResult.bid?.id}
标书标题: ${importResult.bid?.title}
响应状态: ${importResponse.status}

标书已成功导入到数据库！`);
        console.log('导入成功:', importResult);
      } else {
        alert(`❌ API调用失败！
错误状态: ${importResponse.status}
错误信息: ${importResult.error || '未知错误'}

请检查后端服务是否正常运行。`);
        console.error('导入失败:', importResult);
      }

    } catch (error) {
      alert(`❌ API测试出错！
错误信息: ${error.message}

请检查：
1. 后端服务是否启动
2. 网络连接是否正常
3. 是否有有效的用户和公司数据`);
      console.error('API测试错误:', error);
    }
  }

  _handleExportBid(event) {
    const { bidId, format, onProgress, onComplete, onError } = event.detail;
    console.log('导出标书事件:', { bidId, format });

    // 模拟导出过程
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15 + 5; // 每次增加5-20%
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);

        // 模拟文件下载
        setTimeout(() => {
          // 获取当前标书信息
          const currentBid = mockBids.find(bid => bid.id === bidId) || mockBids[0];

          // 创建一个模拟的文件下载
          const content = `标书标题: ${currentBid.title}

项目描述: ${currentBid.description}
项目预算: ${currentBid.project_budget}
项目地点: ${currentBid.project_location}
联系人: ${currentBid.contact_person}
联系电话: ${currentBid.contact_phone}

导出格式: ${format.toUpperCase()}
导出时间: ${new Date().toLocaleString('zh-CN')}

这是一个模拟的导出文件内容。
在实际应用中，这里会是完整的标书内容，包括：
- 项目技术方案
- 商务报价
- 公司资质
- 项目实施计划
- 风险控制措施
等详细内容。`;

          const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;

          // 使用标书名称作为文件名
          const safeTitle = currentBid.title.replace(/[^\w\s-]/g, '').trim();
          const extension = format === 'pdf' ? '.pdf' : '.docx';
          a.download = `${safeTitle}${extension}`;

          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);

          onComplete();
          alert(`导出成功！文件已下载: ${safeTitle}${extension}`);
        }, 500);
      } else {
        onProgress(Math.floor(progress));
      }
    }, 300);
  }

  _handleImageUpload(event) {
    const { file, nodeId, preview } = event.detail;
    console.log('图片上传事件:', { fileName: file.name, fileSize: file.size, nodeId });

    // 模拟文件上传过程
    setTimeout(() => {
      // 在实际应用中，这里会上传文件到服务器并返回URL
      // 现在我们只是使用预览URL
      console.log('图片上传完成，使用预览URL:', preview);
      alert(`图片上传成功: ${file.name}`);
    }, 1000);
  }

  _formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

customElements.define('test-components', TestComponents);
