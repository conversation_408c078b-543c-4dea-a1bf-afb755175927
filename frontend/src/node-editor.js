import { LitElement, html, css } from 'lit';
import '@material/web/button/filled-button.js';
import '@material/web/button/text-button.js';
import '@material/web/iconbutton/icon-button.js';
import '@material/web/icon/icon.js';
import './text-editor.js';
import './image-editor.js';
import './table-editor.js';

/**
 * 节点编辑组件
 * 编辑器容器，传入一个节点（包含内容列表），根据内容类型创建对应的编辑器
 * 只有一个编辑器处于编辑状态，编辑器开始编辑时自动关闭其他编辑器
 */
export class NodeEditor extends LitElement {
  static styles = css`
    :host {
      display: block;
      padding: 16px;
      border: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
      border-radius: 12px;
      background-color: var(--md-sys-color-surface, #ffffff);
    }

    .node-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
    }

    .node-title {
      font-size: 18px;
      font-weight: 500;
      color: var(--md-sys-color-on-surface, #1c1b1f);
    }

    .node-actions {
      display: flex;
      gap: 8px;
      position: relative;
    }

    .content-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .content-item {
      position: relative;
    }

    .empty-state {
      text-align: center;
      padding: 32px 16px;
      color: var(--md-sys-color-on-surface-variant, #49454f);
    }

    .empty-icon {
      font-size: 48px;
      color: var(--md-sys-color-outline, #79747e);
      margin-bottom: 16px;
    }

    .add-content-menu {
      position: absolute;
      top: 100%;
      right: 0;
      z-index: 1000;
      background: var(--md-sys-color-surface-container, #f3f3f3);
      border: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      min-width: 160px;
    }

    .menu-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .menu-item:hover {
      background-color: var(--md-sys-color-surface-container-high, #e6e0e9);
    }

    .menu-item md-icon {
      --md-icon-size: 20px;
    }

    .save-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--md-sys-color-outline-variant, #cac4d0);
    }
  `;

  static properties = {
    node: { type: Object },
    readonly: { type: Boolean },
    // 外部事件处理函数
    onSave: { type: Function },
    onCancel: { type: Function },
    onDelete: { type: Function },
    // 内部状态
    _contentList: { type: Array, state: true },
    _currentEditingIndex: { type: Number, state: true },
    _showAddMenu: { type: Boolean, state: true },
    _hasChanges: { type: Boolean, state: true }
  };

  constructor() {
    super();
    this.node = null;
    this.readonly = false;
    
    // 内部状态
    this._contentList = [];
    this._currentEditingIndex = -1;
    this._showAddMenu = false;
    this._originalContentList = [];
    this._hasChanges = false;
  }

  connectedCallback() {
    super.connectedCallback();
    // 监听点击事件以关闭添加菜单
    document.addEventListener('click', this._handleDocumentClick.bind(this));
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    document.removeEventListener('click', this._handleDocumentClick.bind(this));
  }

  updated(changedProperties) {
    super.updated(changedProperties);
    
    if (changedProperties.has('node')) {
      this._updateFromNodeData();
    }
  }

  _updateFromNodeData() {
    if (this.node && this.node.content) {
      // 如果节点数据包含内容列表
      if (Array.isArray(this.node.content)) {
        this._contentList = [...this.node.content];
      } else {
        // 如果是单个内容，转换为列表格式
        this._contentList = [this.node.content];
      }
    } else {
      this._contentList = [];
    }
    
    // 保存原始状态
    this._originalContentList = JSON.parse(JSON.stringify(this._contentList));
    this._currentEditingIndex = -1;
    this._hasChanges = false;
  }

  _handleDocumentClick(e) {
    if (!this.shadowRoot.contains(e.target)) {
      this._showAddMenu = false;
      this.requestUpdate();
    }
  }

  _onAddContent() {
    this._showAddMenu = !this._showAddMenu;
    this.requestUpdate();
  }

  _onAddContentType(contentType) {
    const newContent = this._createDefaultContent(contentType);
    this._contentList.push(newContent);
    this._showAddMenu = false;
    this._hasChanges = true;
    
    // 自动开始编辑新添加的内容
    this._currentEditingIndex = this._contentList.length - 1;
    this.requestUpdate();
  }

  _createDefaultContent(type) {
    const contentId = `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    switch (type) {
      case 'text':
        return {
          id: contentId,
          type: 'text',
          content: { text: '' }
        };
      case 'image':
        return {
          id: contentId,
          type: 'image',
          content: { imageUrl: '', caption: '' }
        };
      case 'table':
        return {
          id: contentId,
          type: 'table',
          content: { headers: [], data: [], title: '' }
        };
      default:
        return {
          id: contentId,
          type: 'text',
          content: { text: '' }
        };
    }
  }

  // 处理编辑器事件
  _onEditStart(e) {
    const editorElement = e.target;
    const contentIndex = this._findContentIndex(editorElement);
    
    if (contentIndex !== -1) {
      // 关闭其他编辑器
      this._closeAllEditors(contentIndex);
      this._currentEditingIndex = contentIndex;
    }
  }

  _onEditSave(e) {
    const editorElement = e.target;
    const contentIndex = this._findContentIndex(editorElement);
    
    if (contentIndex !== -1) {
      // 更新内容
      this._contentList[contentIndex].content = e.detail.content;
      this._hasChanges = true;
      this._currentEditingIndex = -1;
      this.requestUpdate();
    }
  }

  _onEditCancel(e) {
    this._currentEditingIndex = -1;
    this.requestUpdate();
  }

  _onContentDelete(e) {
    const editorElement = e.target;
    const contentIndex = this._findContentIndex(editorElement);
    
    if (contentIndex !== -1) {
      this._contentList.splice(contentIndex, 1);
      this._hasChanges = true;
      this._currentEditingIndex = -1;
      this.requestUpdate();
    }
  }

  _findContentIndex(editorElement) {
    const contentId = editorElement.nodeId;
    return this._contentList.findIndex(content => content.id === contentId);
  }

  _closeAllEditors(exceptIndex = -1) {
    const editors = this.shadowRoot.querySelectorAll('text-editor, image-editor, table-editor');
    editors.forEach((editor, index) => {
      if (index !== exceptIndex && editor.editing) {
        editor.cancelEdit();
      }
    });
  }

  // 节点保存和取消
  async _saveNode() {
    try {
      // 合并所有内容
      const mergedContent = this._contentList.length === 1 
        ? this._contentList[0] 
        : this._contentList;

      // 触发节点保存事件
      this.dispatchEvent(new CustomEvent('node-save', {
        detail: {
          nodeId: this.node.id,
          content: mergedContent,
          metadata: this.node?.metadata || {}
        },
        bubbles: true,
        composed: true
      }));

      // 调用外部保存处理函数
      if (this.onSave) {
        await this.onSave(mergedContent, this.node?.metadata || {});
      }

      this._hasChanges = false;
      this._originalContentList = JSON.parse(JSON.stringify(this._contentList));
      
    } catch (error) {
      console.error('保存节点失败:', error);
      // 可以添加错误提示
    }
  }

  _cancelNode() {
    // 恢复原始状态
    this._contentList = JSON.parse(JSON.stringify(this._originalContentList));
    this._hasChanges = false;
    this._currentEditingIndex = -1;
    this.requestUpdate();

    // 调用外部取消处理函数
    if (this.onCancel) {
      this.onCancel();
    }
  }

  _deleteNode() {
    if (this.onDelete) {
      this.onDelete();
    }
  }

  render() {
    return html`
      <div class="node-container">
        <!-- 节点头部 -->
        <div class="node-header">
          <div class="node-title">
            ${this.node?.title || '节点编辑'}
          </div>
          <div class="node-actions">
            ${!this.readonly ? html`
              <md-icon-button @click=${this._onAddContent} title="添加内容">
                <md-icon>add</md-icon>
              </md-icon-button>
              <md-icon-button @click=${this._deleteNode} title="删除节点">
                <md-icon>delete</md-icon>
              </md-icon-button>
            ` : ''}

            ${this._showAddMenu ? this._renderAddMenu() : ''}
          </div>
        </div>

        <!-- 内容列表 -->
        <div class="content-list">
          ${this._contentList.length > 0
            ? this._contentList.map((content, index) => this._renderContentItem(content, index))
            : this._renderEmptyState()}
        </div>

        <!-- 保存操作按钮 -->
        ${this._hasChanges ? html`
          <div class="save-actions">
            <md-text-button @click=${this._cancelNode}>
              取消
            </md-text-button>
            <md-filled-button @click=${this._saveNode}>
              保存节点
            </md-filled-button>
          </div>
        ` : ''}
      </div>
    `;
  }

  _renderAddMenu() {
    return html`
      <div class="add-content-menu">
        <div class="menu-item" @click=${() => this._onAddContentType('text')}>
          <md-icon>text_fields</md-icon>
          <span>文本</span>
        </div>
        <div class="menu-item" @click=${() => this._onAddContentType('image')}>
          <md-icon>image</md-icon>
          <span>图片</span>
        </div>
        <div class="menu-item" @click=${() => this._onAddContentType('table')}>
          <md-icon>table_chart</md-icon>
          <span>表格</span>
        </div>
      </div>
    `;
  }

  _renderContentItem(content, index) {
    const isEditing = this._currentEditingIndex === index;

    return html`
      <div class="content-item">
        ${this._renderEditor(content, isEditing)}
      </div>
    `;
  }

  _renderEditor(content, isEditing) {
    const commonProps = {
      nodeId: content.id,
      content: content.content,
      readonly: this.readonly,
      editing: isEditing,
      '@edit-start': this._onEditStart.bind(this),
      '@edit-save': this._onEditSave.bind(this),
      '@edit-cancel': this._onEditCancel.bind(this),
      '@node-delete': this._onContentDelete.bind(this)
    };

    switch (content.type) {
      case 'text':
        return html`
          <text-editor
            .nodeId=${commonProps.nodeId}
            .content=${commonProps.content}
            .readonly=${commonProps.readonly}
            .editing=${commonProps.editing}
            @edit-start=${commonProps['@edit-start']}
            @edit-save=${commonProps['@edit-save']}
            @edit-cancel=${commonProps['@edit-cancel']}
            @node-delete=${commonProps['@node-delete']}
          ></text-editor>
        `;

      case 'image':
        return html`
          <image-editor
            .nodeId=${commonProps.nodeId}
            .content=${commonProps.content}
            .readonly=${commonProps.readonly}
            .editing=${commonProps.editing}
            @edit-start=${commonProps['@edit-start']}
            @edit-save=${commonProps['@edit-save']}
            @edit-cancel=${commonProps['@edit-cancel']}
            @node-delete=${commonProps['@node-delete']}
          ></image-editor>
        `;

      case 'table':
        return html`
          <table-editor
            .nodeId=${commonProps.nodeId}
            .content=${commonProps.content}
            .readonly=${commonProps.readonly}
            .editing=${commonProps.editing}
            @edit-start=${commonProps['@edit-start']}
            @edit-save=${commonProps['@edit-save']}
            @edit-cancel=${commonProps['@edit-cancel']}
            @node-delete=${commonProps['@node-delete']}
          ></table-editor>
        `;

      default:
        return html`
          <div style="padding: 16px; color: #999;">
            不支持的内容类型: ${content.type}
          </div>
        `;
    }
  }

  _renderEmptyState() {
    return html`
      <div class="empty-state">
        <md-icon class="empty-icon">description</md-icon>
        <div>暂无内容</div>
        <div style="margin-top: 8px; font-size: 14px; color: #999;">
          点击上方的添加按钮来添加文本、图片或表格内容
        </div>
      </div>
    `;
  }
}

customElements.define('node-editor', NodeEditor);
