<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件类型调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .file-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>文件类型调试工具</h1>
    <p>用于测试不同文件类型的MIME类型检测</p>

    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>点击选择文件或拖拽文件到此处</p>
        <p><small>支持所有文件类型（用于调试）</small></p>
    </div>

    <input type="file" id="fileInput" class="hidden" onchange="handleFileSelect(event)">

    <div id="results"></div>

    <script>
        // 模拟bid-import组件的新验证逻辑
        function isValidFileType(file) {
            // 获取文件扩展名
            const fileName = file.name.toLowerCase();
            const extension = fileName.split('.').pop();

            // 允许的扩展名
            const allowedExtensions = ['pdf', 'doc', 'docx'];

            // 首先检查扩展名
            if (!allowedExtensions.includes(extension)) {
                return { valid: false, reason: '扩展名不支持' };
            }

            // 然后检查MIME类型（更宽松的检测）
            const allowedMimeTypes = [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                // 添加一些常见的变体
                'application/doc',
                'application/ms-word',
                'application/word',
                'application/vnd.ms-word',
                'application/vnd.ms-word.document.macroEnabled.12',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
                // 某些系统可能返回空的MIME类型
                '',
                'application/octet-stream'
            ];

            // 如果MIME类型匹配，直接通过
            if (file.type && allowedMimeTypes.includes(file.type)) {
                return { valid: true, reason: 'MIME类型匹配' };
            }

            // 如果MIME类型不匹配但扩展名正确，给出警告但仍然允许
            if (allowedExtensions.includes(extension)) {
                return { valid: true, reason: '扩展名正确，MIME类型可能不准确但允许上传' };
            }

            return { valid: false, reason: '文件类型不支持' };
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            const results = document.getElementById('results');

            // 清除之前的结果
            results.innerHTML = '';

            // 显示文件基本信息
            const fileInfo = document.createElement('div');
            fileInfo.className = 'file-info';
            fileInfo.innerHTML = `
                <h3>文件信息</h3>
                <p><strong>文件名:</strong> ${file.name}</p>
                <p><strong>文件大小:</strong> ${formatFileSize(file.size)}</p>
                <p><strong>MIME类型:</strong> ${file.type || '未知/空'}</p>
                <p><strong>最后修改时间:</strong> ${new Date(file.lastModified).toLocaleString()}</p>
            `;
            results.appendChild(fileInfo);

            // 使用新的验证逻辑
            const validation = isValidFileType(file);
            const validationResult = document.createElement('div');

            if (validation.valid) {
                validationResult.className = 'success';
                validationResult.innerHTML = `
                    <h3>✅ 验证通过</h3>
                    <p><strong>原因:</strong> ${validation.reason}</p>
                    <p>该文件将被bid-import组件接受</p>
                `;
            } else {
                validationResult.className = 'error';
                validationResult.innerHTML = `
                    <h3>❌ 验证失败</h3>
                    <p><strong>原因:</strong> ${validation.reason}</p>
                    <p>该文件不会被bid-import组件接受</p>
                `;
            }
            results.appendChild(validationResult);

            // 显示文件扩展名信息
            const extensionInfo = document.createElement('div');
            extensionInfo.className = 'file-info';
            const extension = file.name.split('.').pop().toLowerCase();
            extensionInfo.innerHTML = `
                <h3>扩展名分析</h3>
                <p><strong>文件扩展名:</strong> .${extension}</p>
                <p><strong>预期MIME类型:</strong></p>
                <ul>
                    <li>.pdf → application/pdf</li>
                    <li>.doc → application/msword</li>
                    <li>.docx → application/vnd.openxmlformats-officedocument.wordprocessingml.document</li>
                </ul>
            `;
            results.appendChild(extensionInfo);

            // 如果是DOC/DOCX文件但MIME类型不匹配，显示可能的原因
            if ((extension === 'doc' || extension === 'docx') && !allowedTypes.includes(file.type)) {
                const troubleshoot = document.createElement('div');
                troubleshoot.className = 'error';
                troubleshoot.innerHTML = `
                    <h3>🔍 问题诊断</h3>
                    <p>检测到Word文档但MIME类型不匹配，可能的原因：</p>
                    <ul>
                        <li>文件扩展名与实际格式不符</li>
                        <li>文件已损坏</li>
                        <li>浏览器MIME类型检测问题</li>
                        <li>文件是其他格式但使用了.doc/.docx扩展名</li>
                    </ul>
                    <p><strong>建议:</strong> 尝试用Microsoft Word重新保存文件，或检查文件是否完整。</p>
                `;
                results.appendChild(troubleshoot);
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 支持拖拽
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#007bff';
            uploadArea.style.backgroundColor = '#f8f9fa';
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.borderColor = '#ccc';
            uploadArea.style.backgroundColor = '';
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const fileInput = document.getElementById('fileInput');
                fileInput.files = files;
                handleFileSelect({ target: { files: files } });
            }
        });
    </script>
</body>
</html>
