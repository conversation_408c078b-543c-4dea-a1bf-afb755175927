<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>易中2.0（easywin）- 用户中心</title>
    <link rel="icon" href="/favicon.ico" />
    <link rel="stylesheet" href="/node_modules/material-symbols/index.css" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script type="module" src="/src/easywin-app.js"></script>
    <style>
      :root {
        --primary-color: #2563eb;
        --primary-dark: #1d4ed8;
        --primary-light: #3b82f6;
        --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        margin: 0;
        font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: var(--bg-gradient);
        min-height: 100vh;
        position: relative;
        overflow-x: hidden;
      }

      /* 专业背景效果 - 只在未登录状态显示 */
      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/><circle cx="600" cy="100" r="80" fill="url(%23a)"/><circle cx="100" cy="600" r="110" fill="url(%23a)"/></svg>');
        opacity: 0.3;
        z-index: -1;
      }

      /* 动态粒子效果 */
      body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
          radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
        animation: float 6s ease-in-out infinite;
        z-index: -1;
      }

      @keyframes float {
        0%, 100% {
          transform: translateY(0px) rotate(0deg);
        }
        33% {
          transform: translateY(-10px) rotate(1deg);
        }
        66% {
          transform: translateY(5px) rotate(-1deg);
        }
      }

      /* 当用户已登录时，隐藏背景效果，使用简单背景 */
      body.authenticated {
        background: #f5f5f5;
      }

      body.authenticated::before,
      body.authenticated::after {
        display: none;
      }

      /* 登录页面的品牌标识 */
      .login-brand {
        position: fixed;
        top: 40px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 5; /* 降低z-index，确保不会覆盖登录对话框 */
        text-align: center;
        color: white;
      }

      .login-brand h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .login-brand p {
        font-size: 1.1rem;
        opacity: 0.9;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .login-brand {
          top: 10px; /* 在小屏幕上进一步提高位置，避免与登录对话框重叠 */
        }

        .login-brand h1 {
          font-size: 1.8rem; /* 稍微减小字体大小以节省空间 */
        }

        .login-brand p {
          font-size: 0.9rem; /* 稍微减小字体大小以节省空间 */
        }
      }

      /* 在更小的屏幕上进一步调整 */
      @media (max-width: 480px) {
        .login-brand {
          top: 5px; /* 在极小屏幕上进一步提高位置 */
        }

        .login-brand h1 {
          font-size: 1.6rem;
        }

        .login-brand p {
          font-size: 0.8rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- 登录页面品牌标识 -->
    <div class="login-brand" id="loginBrand">
      <h1>易中2.0</h1>
      <p>智能标书制作平台</p>
    </div>

    <easywin-app></easywin-app>

    <script>
      // 监听登录状态变化，动态调整背景
      function updateBackgroundForAuthState() {
        const token = localStorage.getItem('token');
        const body = document.body;
        const loginBrand = document.getElementById('loginBrand');

        if (token) {
          // 已登录状态
          body.classList.add('authenticated');
          if (loginBrand) {
            loginBrand.style.display = 'none';
          }
        } else {
          // 未登录状态
          body.classList.remove('authenticated');
          if (loginBrand) {
            loginBrand.style.display = 'block';
          }
        }
      }

      // 页面加载时检查
      updateBackgroundForAuthState();

      // 监听storage变化（当在其他标签页登录/退出时）
      window.addEventListener('storage', function(e) {
        if (e.key === 'token') {
          updateBackgroundForAuthState();
        }
      });

      // 监听自定义事件（当在当前页面登录/退出时）
      window.addEventListener('auth-state-changed', updateBackgroundForAuthState);

      // 定期检查（作为备用方案）
      setInterval(updateBackgroundForAuthState, 1000);
    </script>
  </body>
</html>
