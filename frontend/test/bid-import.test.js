import { expect } from '@esm-bundle/chai';
import { fixture, html } from '@open-wc/testing';
import '../src/bid-import.js';

describe('BidImport', () => {
  let element;

  beforeEach(async () => {
    element = await fixture(html`<bid-import></bid-import>`);
  });

  it('should render with initial state', () => {
    expect(element).to.exist;
    expect(element.selectedFile).to.be.null;
    expect(element.parsedContent).to.be.null;
    expect(element.bidTitle).to.equal('');
    expect(element.bidDescription).to.equal('');
    expect(element.loading).to.be.false;
    expect(element.uploading).to.be.false;
    expect(element.parsing).to.be.false;
  });

  it('should show file selection initially', () => {
    const uploadArea = element.shadowRoot.querySelector('.upload-area');
    expect(uploadArea).to.exist;

    const fileInput = element.shadowRoot.querySelector('input[type="file"]');
    expect(fileInput).to.exist;
    expect(fileInput.accept).to.equal('.pdf,.doc,.docx');

    const uploadText = element.shadowRoot.querySelector('.upload-text');
    expect(uploadText).to.exist;
  });

  it('should handle valid file types with correct MIME', async () => {
    const validFile = new File(['test content'], 'test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    element._handleFile(validFile);
    await element.updateComplete;

    expect(element.error).to.equal('');
    expect(element.uploading).to.be.true;
  });

  it('should handle valid file types with incorrect MIME but correct extension', async () => {
    const validFile = new File(['test content'], 'test.docx', {
      type: 'application/octet-stream'  // 错误的MIME类型但正确的扩展名
    });

    element._handleFile(validFile);
    await element.updateComplete;

    expect(element.error).to.equal('');
    expect(element.uploading).to.be.true;
  });

  it('should handle DOC files with various MIME types', async () => {
    const docFile1 = new File(['test content'], 'test.doc', {
      type: 'application/msword'
    });

    const docFile2 = new File(['test content'], 'test.doc', {
      type: 'application/vnd.ms-word'
    });

    const docFile3 = new File(['test content'], 'test.doc', {
      type: ''  // 空MIME类型
    });

    element._handleFile(docFile1);
    await element.updateComplete;
    expect(element.error).to.equal('');

    element._removeFile();

    element._handleFile(docFile2);
    await element.updateComplete;
    expect(element.error).to.equal('');

    element._removeFile();

    element._handleFile(docFile3);
    await element.updateComplete;
    expect(element.error).to.equal('');
  });

  it('should reject invalid file types', async () => {
    const invalidFile = new File(['test content'], 'test.txt', {
      type: 'text/plain'
    });

    element._handleFile(invalidFile);
    await element.updateComplete;

    expect(element.error).to.include('请选择 PDF、DOC 或 DOCX 格式的文件');
    expect(element.selectedFile).to.be.null;
  });

  it('should reject oversized files', async () => {
    const oversizedFile = new File(['x'.repeat(60 * 1024 * 1024)], 'test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    element._handleFile(oversizedFile);
    await element.updateComplete;

    expect(element.error).to.include('文件大小不能超过50MB');
    expect(element.selectedFile).to.be.null;
  });

  it('should format file size correctly', () => {
    expect(element._formatFileSize(0)).to.equal('0 Bytes');
    expect(element._formatFileSize(1024)).to.equal('1 KB');
    expect(element._formatFileSize(1024 * 1024)).to.equal('1 MB');
    expect(element._formatFileSize(1536)).to.equal('1.5 KB');
  });

  it('should handle file selection through input', async () => {
    const mockFile = new File(['test content'], 'test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    // Mock file input change event
    const fileInput = element.shadowRoot.querySelector('input[type="file"]');
    Object.defineProperty(fileInput, 'files', {
      value: [mockFile],
      writable: false,
    });

    const changeEvent = new Event('change');
    fileInput.dispatchEvent(changeEvent);

    await element.updateComplete;

    expect(element.uploading).to.be.true;
    expect(element.error).to.equal('');
  });

  it('should handle drag and drop events', async () => {
    const uploadArea = element.shadowRoot.querySelector('.upload-area');

    // Test drag over
    const dragOverEvent = new DragEvent('dragover', {
      bubbles: true,
      cancelable: true
    });
    uploadArea.dispatchEvent(dragOverEvent);

    await element.updateComplete;
    expect(uploadArea.classList.contains('dragover')).to.be.true;

    // Test drag leave
    const dragLeaveEvent = new DragEvent('dragleave', {
      bubbles: true,
      cancelable: true
    });
    uploadArea.dispatchEvent(dragLeaveEvent);

    await element.updateComplete;
    expect(uploadArea.classList.contains('dragover')).to.be.false;
  });

  it('should show file information when file is selected', async () => {
    const mockFile = new File(['test content'], 'test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });
    Object.defineProperty(mockFile, 'size', { value: 1024 * 1024 }); // 1MB

    element.selectedFile = mockFile;
    await element.updateComplete;

    const fileName = element.shadowRoot.querySelector('.file-name');
    const fileSize = element.shadowRoot.querySelector('.file-meta');

    if (fileName && fileSize) {
      expect(fileName.textContent).to.equal('test.docx');
      expect(fileSize.textContent).to.equal('1 MB');
    }
  });

  it('should validate bid title before import', async () => {
    const mockFile = new File(['test content'], 'test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    element.selectedFile = mockFile;
    element.parsedContent = { preview: 'Test content' };
    element.bidTitle = '';

    await element.updateComplete;

    // Try to import without title
    await element._importBid();
    expect(element.error).to.include('请输入标书标题');
  });

  it('should emit bid-import event on successful import', async () => {
    const mockFile = new File(['test content'], 'test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    element.selectedFile = mockFile;
    element.parsedContent = { preview: 'Test content' };
    element.bidTitle = '测试标书';
    element.bidDescription = '测试描述';

    let eventFired = false;
    let eventDetail = null;

    element.addEventListener('bid-import', (e) => {
      eventFired = true;
      eventDetail = e.detail;
    });

    await element._importBid();

    expect(eventFired).to.be.true;
    expect(eventDetail).to.deep.include({
      file: mockFile,
      title: '测试标书',
      description: '测试描述'
    });
  });

  it('should handle file removal', async () => {
    const mockFile = new File(['test content'], 'test.docx', {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    });

    element.selectedFile = mockFile;
    await element.updateComplete;

    element._removeFile();
    await element.updateComplete;

    expect(element.selectedFile).to.be.null;
    expect(element.bidTitle).to.equal('');
    expect(element.bidDescription).to.equal('');
  });

  it('should format file size correctly', () => {
    expect(element._formatFileSize(0)).to.equal('0 Bytes');
    expect(element._formatFileSize(1024)).to.equal('1 KB');
    expect(element._formatFileSize(1024 * 1024)).to.equal('1 MB');
    expect(element._formatFileSize(1536)).to.equal('1.5 KB');
  });
});
