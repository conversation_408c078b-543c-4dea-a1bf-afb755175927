import { expect } from '@esm-bundle/chai';
import { fixture, html } from '@open-wc/testing';
import '../src/node-editor.js';

describe('NodeEditor', () => {
  let element;

  beforeEach(async () => {
    element = await fixture(html`
      <node-editor></node-editor>
    `);
  });

  it('should render empty state when no content', async () => {
    const emptyState = element.shadowRoot.querySelector('.empty-state');
    expect(emptyState).to.exist;
    expect(emptyState.textContent).to.include('暂无内容');
  });

  it('should display node title', async () => {
    element.node = {
      title: '测试节点',
      content: []
    };
    await element.updateComplete;

    const title = element.shadowRoot.querySelector('.node-title');
    expect(title.textContent.trim()).to.equal('测试节点');
  });

  it('should render content items', async () => {
    element.node = {
      title: '测试节点',
      content: [
        {
          id: 'content-1',
          type: 'text',
          content: { text: '测试文本' }
        }
      ]
    };
    await element.updateComplete;
    // 等待子组件渲染
    await new Promise(resolve => setTimeout(resolve, 100));

    // 检查内容列表是否正确更新
    expect(element._contentList).to.have.length(1);
    expect(element._contentList[0].type).to.equal('text');

    // 检查是否有内容项被渲染
    const contentItems = element.shadowRoot.querySelectorAll('.content-item');
    expect(contentItems).to.have.length(1);
  });

  it('should show add content menu when add button clicked', async () => {
    // 确保不是只读模式
    element.readonly = false;
    element.node = { title: '测试', content: [] };
    await element.updateComplete;

    // 直接调用方法而不是点击按钮
    element._onAddContent();
    await element.updateComplete;

    expect(element._showAddMenu).to.be.true;

    const menu = element.shadowRoot.querySelector('.add-content-menu');
    expect(menu).to.exist;

    const menuItems = menu.querySelectorAll('.menu-item');
    expect(menuItems).to.have.length(3); // text, image, table
  });

  it('should add new content when menu item clicked', async () => {
    element.node = { title: '测试', content: [] };
    element.readonly = false;
    await element.updateComplete;

    // 直接调用添加内容方法
    element._onAddContentType('text');
    await element.updateComplete;

    expect(element._contentList).to.have.length(1);
    expect(element._contentList[0].type).to.equal('text');
    expect(element._currentEditingIndex).to.equal(0);
    expect(element._hasChanges).to.be.true;
    expect(element._showAddMenu).to.be.false; // 菜单应该关闭
  });

  it('should handle edit start event', async () => {
    element.node = {
      title: '测试',
      content: [
        { id: 'content-1', type: 'text', content: { text: '测试' } }
      ]
    };
    await element.updateComplete;
    await new Promise(resolve => setTimeout(resolve, 0));

    const textEditor = element.shadowRoot.querySelector('text-editor');
    expect(textEditor).to.exist;

    // 模拟编辑开始事件
    textEditor.dispatchEvent(new CustomEvent('edit-start', {
      detail: { nodeId: 'content-1' },
      bubbles: true
    }));

    expect(element._currentEditingIndex).to.equal(0);
  });

  it('should handle edit save event', async () => {
    element.node = {
      title: '测试',
      content: [
        { id: 'content-1', type: 'text', content: { text: '原始文本' } }
      ]
    };
    await element.updateComplete;
    await new Promise(resolve => setTimeout(resolve, 0));

    const textEditor = element.shadowRoot.querySelector('text-editor');
    expect(textEditor).to.exist;

    // 模拟编辑保存事件
    textEditor.dispatchEvent(new CustomEvent('edit-save', {
      detail: {
        nodeId: 'content-1',
        content: { text: '更新后的文本' }
      },
      bubbles: true
    }));

    expect(element._contentList[0].content.text).to.equal('更新后的文本');
    expect(element._hasChanges).to.be.true;
    expect(element._currentEditingIndex).to.equal(-1);
  });

  it('should handle content deletion', async () => {
    element.node = {
      title: '测试',
      content: [
        { id: 'content-1', type: 'text', content: { text: '测试1' } },
        { id: 'content-2', type: 'text', content: { text: '测试2' } }
      ]
    };
    await element.updateComplete;
    await new Promise(resolve => setTimeout(resolve, 0));

    const textEditor = element.shadowRoot.querySelector('text-editor');
    expect(textEditor).to.exist;

    // 模拟删除事件
    textEditor.dispatchEvent(new CustomEvent('node-delete', {
      bubbles: true
    }));

    expect(element._contentList).to.have.length(1);
    expect(element._hasChanges).to.be.true;
  });

  it('should show save actions when has changes', async () => {
    element.node = { title: '测试', content: [] };
    element._hasChanges = true;
    await element.updateComplete;

    const saveActions = element.shadowRoot.querySelector('.save-actions');
    expect(saveActions).to.exist;

    const cancelButton = saveActions.querySelector('md-text-button');
    const saveButton = saveActions.querySelector('md-filled-button');
    expect(cancelButton).to.exist;
    expect(saveButton).to.exist;
  });

  it('should emit node-save event when save button clicked', async () => {
    let savedData = null;
    element.addEventListener('node-save', (e) => {
      savedData = e.detail;
    });

    element.node = {
      id: 'node-123',
      title: '测试',
      content: [{ id: 'content-1', type: 'text', content: { text: '测试' } }],
      metadata: { test: true }
    };
    element._hasChanges = true;
    await element.updateComplete;

    const saveButton = element.shadowRoot.querySelector('md-filled-button');
    saveButton.click();
    await element.updateComplete;

    expect(savedData).to.exist;
    expect(savedData.nodeId).to.equal(element.node.id);
    expect(savedData.content).to.deep.equal(element._contentList[0]);
    expect(savedData.metadata).to.deep.equal({ test: true });
  });

  it('should reset changes when cancel button clicked', async () => {
    const originalContent = [
      { id: 'content-1', type: 'text', content: { text: '原始' } }
    ];

    element.node = {
      title: '测试',
      content: originalContent
    };
    await element.updateComplete;

    // 修改内容
    element._contentList[0].content.text = '修改后';
    element._hasChanges = true;
    await element.updateComplete;
    await new Promise(resolve => setTimeout(resolve, 0));

    const cancelButton = element.shadowRoot.querySelector('md-text-button');
    expect(cancelButton).to.exist;
    cancelButton.click();
    await element.updateComplete;

    expect(element._contentList[0].content.text).to.equal('原始');
    expect(element._hasChanges).to.be.false;
  });

  it('should hide add button in readonly mode', async () => {
    element.readonly = true;
    await element.updateComplete;

    const addButton = element.shadowRoot.querySelector('md-icon-button[title="添加内容"]');
    expect(addButton).to.not.exist;
  });

  it('should handle single content as array', async () => {
    element.node = {
      title: '测试',
      content: { id: 'content-1', type: 'text', content: { text: '单个内容' } }
    };
    await element.updateComplete;

    expect(element._contentList).to.have.length(1);
    expect(element._contentList[0].content.text).to.equal('单个内容');
  });
});
