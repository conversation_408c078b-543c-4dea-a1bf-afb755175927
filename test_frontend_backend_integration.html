<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户信息更新测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>用户信息更新测试</h1>
    
    <div class="test-section">
        <h3>1. 登录测试</h3>
        <input type="tel" id="phone" placeholder="手机号" value="13800138000">
        <input type="text" id="code" placeholder="验证码" value="123456">
        <button class="btn-primary" onclick="testLogin()">登录</button>
        <div id="loginResult"></div>
    </div>

    <div class="test-section">
        <h3>2. 用户信息更新测试</h3>
        <input type="text" id="username" placeholder="用户名" value="测试用户更新">
        <input type="email" id="email" placeholder="邮箱" value="<EMAIL>">
        <input type="tel" id="phoneUpdate" placeholder="手机号" value="">
        <button class="btn-primary" onclick="testUpdateProfile()">更新用户信息</button>
        <div id="updateResult"></div>
    </div>

    <div class="test-section">
        <h3>3. 错误情况测试</h3>
        <button class="btn-secondary" onclick="testEmptyUpdate()">测试空数据</button>
        <button class="btn-secondary" onclick="testDuplicateUsername()">测试重复用户名</button>
        <button class="btn-secondary" onclick="testDuplicateEmail()">测试重复邮箱</button>
        <div id="errorTestResult"></div>
    </div>

    <div class="test-section">
        <h3>4. 当前状态</h3>
        <button class="btn-secondary" onclick="getCurrentUser()">获取当前用户信息</button>
        <div id="currentUserResult"></div>
    </div>

    <script>
        let currentToken = null;
        const API_BASE = '/api';

        async function apiCall(endpoint, method = 'GET', body = null) {
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (currentToken) {
                headers['Authorization'] = `Bearer ${currentToken}`;
            }

            const config = {
                method,
                headers
            };

            if (body) {
                config.body = JSON.stringify(body);
            }

            try {
                const response = await fetch(`${API_BASE}${endpoint}`, config);
                const data = await response.json();
                return { status: response.status, data };
            } catch (error) {
                return { status: 0, error: error.message };
            }
        }

        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            const isSuccess = result.status >= 200 && result.status < 300;
            element.className = `test-section ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = `
                <strong>状态码:</strong> ${result.status}<br>
                <strong>响应:</strong><br>
                <pre>${JSON.stringify(result.data || result.error, null, 2)}</pre>
            `;
        }

        async function testLogin() {
            const phone = document.getElementById('phone').value;
            const code = document.getElementById('code').value;
            
            const result = await apiCall('/auth/login', 'POST', { phone, code });
            
            if (result.status === 200 && result.data.token) {
                currentToken = result.data.token;
            }
            
            displayResult('loginResult', result);
        }

        async function testUpdateProfile() {
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const phone = document.getElementById('phoneUpdate').value;
            
            const updateData = {};
            if (username) updateData.username = username;
            if (email) updateData.email = email;
            if (phone) updateData.phone = phone;
            
            const result = await apiCall('/user/profile', 'PUT', updateData);
            displayResult('updateResult', result);
        }

        async function testEmptyUpdate() {
            const result = await apiCall('/user/profile', 'PUT', {});
            displayResult('errorTestResult', result);
        }

        async function testDuplicateUsername() {
            const result = await apiCall('/user/profile', 'PUT', { username: '测试用户' });
            displayResult('errorTestResult', result);
        }

        async function testDuplicateEmail() {
            const result = await apiCall('/user/profile', 'PUT', { email: '<EMAIL>' });
            displayResult('errorTestResult', result);
        }

        async function getCurrentUser() {
            const result = await apiCall('/auth/me');
            displayResult('currentUserResult', result);
        }
    </script>
</body>
</html>
